import eslint from 'vite-plugin-eslint';
import { mergeConfig } from 'vite';
import AutoImport from 'unplugin-auto-import/vite';
import { ArcoResolver } from 'unplugin-vue-components/resolvers';
import Components from 'unplugin-vue-components/vite';
import baseConfig from './vite.config.base';

export default mergeConfig(
  {
    mode: 'development',
    server: {
      fs: {
        strict: true,
      },
    },
    plugins: [
      // AutoImport({
      //   resolvers: [ArcoResolver()],
      // }),
      // Components({
      //   resolvers: [
      //     ArcoResolver({
      //       sideEffect: true,
      //     }),
      //   ],
      // }),
      eslint({
        cache: false,
        include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
        exclude: ['node_modules'],
      }),
    ],
  },
  baseConfig,
);
