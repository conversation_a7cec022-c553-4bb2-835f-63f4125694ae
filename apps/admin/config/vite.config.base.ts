import { resolve } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import svgLoader from 'vite-svg-loader';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';

export default defineConfig({
  envDir: '../../env',
  plugins: [vue(), svgLoader({ svgoConfig: {} })],
  logLevel: 'error',
  base: process.env.VITE_APP_TARGET_RELEASE === 'pc' ? './' : '/',
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, '../src'),
      },
      {
        find: 'assets',
        replacement: resolve(__dirname, '../src/assets'),
      },
      {
        find: 'vue-i18n',
        // replacement: 'vue-i18n/dist/vue-i18n.cjs.js', // Resolve the i18n warning issue
        replacement: 'vue-i18n/dist/vue-i18n.esm-bundler.js',
      },
      {
        find: 'vue',
        replacement: 'vue/dist/vue.esm-bundler.js', // compile template
      },
    ],
    extensions: ['.ts', '.js', '.vue'],
  },
  define: {
    'process.env': {},
    'eval': 'window.eval',
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${resolve('src/assets/style/breakpoint.less')}";`,
        },
        javascriptEnabled: true,
      },
      scss: {
        api: 'modern-compiler',
      },
    },
    postcss: {
      // eslint-disable-next-line global-require
      plugins: [tailwindcss, autoprefixer],
    },
  },
  build: {
    sourcemap: false,
    minify: true,
    rollupOptions: {
      output: {
        manualChunks: (id: string) => {
          if (id.toLowerCase().includes('hiprint')) {
            return 'hiPrint';
          }
          return undefined;
        },
      },
    },
  },
});
