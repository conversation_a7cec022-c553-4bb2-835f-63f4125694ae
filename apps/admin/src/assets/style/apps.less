@textShadowColor: #1a255a;
.apps {
  width: 800px;
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;

  .app-item {
    width: 20%;
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all linear 0.2s;

    .icon {
      box-shadow: 0 0 10px 0 rgba(@textShadowColor, 0.5);
      width: 90px;
      height: 90px;
      background: #fff;
      border-radius: 8px;
      transition: all linear 0.2s;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0.9;

      svg {
        fill: url(#icon-gradient);
        width: 60px;
        height: 60px;
        :deep {
          rect,
          circle,
          path,
          line {
            stroke: url(#icon-gradient) !important;
            //fill: url(#icon-gradient) !important;
          }
        }
      }
    }

    .text {
      margin-top: 8px;
      transition: all linear 0.2s;
      color: #f2f2f2;
    }

    &:hover {
      transform: scale(1.1);
      .icon {
        box-shadow: 0 0 10px 0 rgba(@textShadowColor, 1);
        opacity: 1;
      }

      .text {
        text-shadow: 0 0 10px rgba(@textShadowColor, 1);
        color: #fff;
      }
    }
  }
}