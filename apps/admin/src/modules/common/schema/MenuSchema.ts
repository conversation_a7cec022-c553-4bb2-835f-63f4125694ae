import { CustomSchema } from '@repo/infrastructure/types';

const menuSchema: CustomSchema = {
  api: '/common/menu',
  fieldsMap: {
    parentId: {
      editable: true,
      label: 'common.parentNode',
      valueType: 'Integer',
      inputWidget: 'treeSelectInput',
      treeSourceApi: '/common/menu',
      inputWidgetProps: {
        allowClear: true,
      },
    },
    icon: {
      valueType: 'String',
      inputWidgetProps: {
        allowClear: true,
      },
    },
    children: {
      valueType: 'Collection',
      visibleInDetail: false,
      visibleInTable: false,
    },
    allDescendantsIdWithSelf: {
      visibleInTable: false,
      visibleInDetail: false,
    },
  },
  rowActions: [
    {
      key: 'edit',
      label: 'common.edit',
      icon: 'icon-edit',
    },
  ],
  listViewProps: {
    showIndex: false,
    visibleColumns: ['name', 'menuIcon', 'localeKey', 'path', 'authority', 'sort'],
  },
};
// export default { menuSchema };

export default { menuSchema };
