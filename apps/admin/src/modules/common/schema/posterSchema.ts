import { CustomSchema } from '@repo/infrastructure/types';
import dayjs from 'dayjs';
import Constants from '@repo/infrastructure/constants';

const posterSchema: CustomSchema = {
  api: '/common/poster',
  rowActions: [
    { key: 'design', label: '海报页面设计', handler() {} },
    { key: 'qrcode', label: '海报二维码', handler() {} },
  ],
  fieldsMap: {
    pageConfig: {
      visibleInDetail: false,
      visibleInTable: false,
      visibleInForm: false,
    },
    uuid: {
      visibleInForm: false,
    },
    widgets: {
      visibleInDetail: false,
      visibleInTable: false,
      visibleInForm: false,
    },
    expireTime: {
      inputWidgetProps: {
        showTime: true,
      },
      listProps: {
        width: 200,
      },
      displayProps: {
        format: Constants.FULL_DATE_FORMAT,
      },
    },
  },
};

export default { posterSchema };
