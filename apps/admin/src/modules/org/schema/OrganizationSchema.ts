import { CustomSchema } from '@repo/infrastructure/types';
import { DEFAULT_LIST_FIELDS } from '@repo/infrastructure/constants';

const organizationSchema: CustomSchema = {
  api: '/org/organization',
  formViewProps: {
    fieldsGrouping: [
      {
        label: 'common.form.basicInfo',
        fields: ['name', 'logo'],
        colSpan: 12,
      },
    ],
  },
  listViewProps: {
    visibleColumns: ['simpleName', 'name', 'bindDomain', 'description', ...DEFAULT_LIST_FIELDS],
  },
  quickSearchProps: {
    enabled: true,
    fields: ['name', 'simpleName', 'bindDomain', 'description'],
  },
  fieldsMap: {},
  detailViewProps: {
    type: 'drawer',
    width: 800,
    columns: 2,
  },
};
export default { organizationSchema };
