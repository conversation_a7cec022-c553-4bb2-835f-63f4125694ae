import { CustomSchema } from '@repo/infrastructure/types';
import api from '@repo/infrastructure/openapi';
import { Message } from '@arco-design/web-vue';
import { usePrompt } from '@repo/ui/components';
import { useNotificationStore } from '@repo/infrastructure/store';

const visitReservationSchema: CustomSchema = {
  api: '/guardian/visitReservation',
  fieldsMap: {
    status: {
      inputWidget: 'selectInput',
      dataType: 'Enum',
      valueType: 'Enum',
      inputWidgetProps: {
        options: [
          { label: '待审核', value: 'pending' },
          { label: '已通过', value: 'approved' },
          { label: '已拒绝', value: 'refused' },
        ],
      },
    },
    visitDate: {
      displayProps: {
        format: 'YYYY-MM-DD',
        fullFormat: '预计到访：YYYY年MM月DD日',
      },
    },
    guardianId: {
      visibleInTable: false,
      invisible: true,
    },
    visitorMobile: {
      visibleInTable: false,
    },
    respondentMobile: {
      visibleInTable: false,
    },
    visitTime: {
      visibleInTable: false,
    },
  },
  rowActions: [
    {
      label: '审核通过',
      key: 'approve',
      expose: true,
      btnProps: {
        type: 'primary',
      },
      visible: (record) => record.status === 'pending',
      handler: async (record: any, loadData: any) => {
        const { prompt } = usePrompt();
        const remark = await prompt({
          title: '审核备注',
          placeholder: '请输入审核备注',
          inputWidget: 'textarea',
        });
        await api.guardianController.updateGuardianVisitReservationStatus({
          id: record.id,
          status: 'approved' as any,
          remark: remark as string,
        });
        Message.success('已审核通过');
        if (typeof loadData === 'function') {
          await loadData();
        }

        const notificationStore = useNotificationStore();
        await notificationStore.refresh();
      },
    },
    {
      label: '拒绝',
      key: 'refuse',
      expose: true,
      btnProps: {
        status: 'danger',
      },
      visible: (record) => record.status === 'pending',
      handler: async (record: any, loadData: any) => {
        const { prompt } = usePrompt();
        const remark = await prompt({
          title: '审核备注',
          placeholder: '请输入审核备注',
          inputWidget: 'textarea',
        });
        await api.guardianController.updateGuardianVisitReservationStatus({
          id: record.id,
          status: 'refused' as any,
          remark: remark as string,
        });
        Message.error('已拒绝');
        if (typeof loadData === 'function') {
          await loadData();
        }
        const notificationStore = useNotificationStore();
        await notificationStore.refresh();
      },
    },
    {
      key: 'edit',
      visible: false,
    },
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'delete',
      visible: false,
    },
  ],
};

export default { visitReservationSchema };
