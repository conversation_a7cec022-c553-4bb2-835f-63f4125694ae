import { CustomSchema } from '@repo/infrastructure/types';
import { weappVisibleInDisplayProps, weappVisibleInOptions } from '../guardian';

const announcementSchema: CustomSchema = {
  api: '/guardian/announcement',
  modulePath: '/manage/center/weapp/announcement',
  fieldsMap: {
    content: {
      visibleInTable: false,
      inputWidget: 'richInput',
    },
    visibleIn: {
      inputWidget: 'checkboxInput',
      inputWidgetProps: {
        options: weappVisibleInOptions,
      },
      displayProps: {
        ...weappVisibleInDisplayProps,
        detailSpan: 2,
      },
    },
  },
};

export default { announcementSchema };
