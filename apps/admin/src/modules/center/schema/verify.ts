import { CustomSchema } from '@repo/infrastructure/types';
import { Message } from '@arco-design/web-vue';
import { usePrompt } from '@repo/ui/components';
import { useNotificationStore } from '@repo/infrastructure/store';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';

const applicationTypes = {
  TourGuide: '巡回指导',
  TourGuideIndividual: '个案回访',
  D2dService: '上门服务',
};

const rawPermissions = ['advisoryService:verify:'];

type UpdateApplicationStatusRo = {
  id?: number;
  remark?: string;
  status?: any;
};
interface UpdateApplicationStatusOptions {
  applicationType?: string;
}

/**
 * 更新状态
 *
 * @param body 更新应用程序状态请求体
 * @param options 可选参数，请求配置选项
 * @returns 返回请求结果
 */
async function updateApplicationStatus(body: UpdateApplicationStatusRo, options?: UpdateApplicationStatusOptions) {
  const applicationType = options?.applicationType;
  let basePath = 'tourGuide';
  if (applicationType === 'D2dService') {
    basePath = 'd2dService';
  } else if (applicationType === 'TourGuideIndividual') {
    basePath = 'tourGuideIndividual';
  }
  const url = `/resourceRoom/${basePath}/updateStatus/${body.id}`;
  return request<any>(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    ...(options || {}),
  });
}

/**
 * 处理审核操作
 *
 * @param record 待审核记录
 * @param loadData 加载数据函数
 * @param status 审核状态
 */
async function handleApprove(record: any, loadData: any, status: string) {
  const { prompt } = usePrompt();
  const title = status === 'Approved' ? '同意' : '拒绝';
  const remark = await prompt({
    title: `${title}申请`,
    placeholder: '请输入审核备注',
    inputWidget: 'textarea',
  });

  await updateApplicationStatus(
    {
      id: record.id,
      status: status as any,
      remark: remark as string,
    },
    {
      applicationType: record.applicationType,
    },
  );
  Message.success('操作成功');
  if (typeof loadData === 'function') {
    await loadData();
  }

  const notificationStore = useNotificationStore();
  await notificationStore.refresh();
}

const operationRecordsColumns = [
  {
    label: '操作人',
    key: 'operatorName',
  },
  {
    label: '操作内容',
    key: 'operation',
  },
  {
    label: '备注说明',
    key: 'remark',
  },
  {
    label: '时间',
    key: 'date',
  },
];
// 申请原因
const applyReasonColumns = [
  {
    label: '内容',
    key: 'content',
  },
  {
    label: '选择',
    key: 'checked',
    dataType: 'Boolean',
    valueType: 'Boolean',
  },
];

function isShowByType(record: any, type: string): boolean {
  return record.applicationType === type;
}

const verify: CustomSchema = {
  api: '/resourceRoom/baseAllApplication',
  requestApi: {
    list: '/resourceRoom/baseAllApplication/getAll',
  },
  permissions: {
    create: rawPermissions.map((item) => `${item}create`),
    update: rawPermissions.map((item) => `${item}update`),
    delete: rawPermissions.map((item) => `${item}delete`),
  },
  rowActions: [
    {
      key: 'approved',
      label: '同意',
      expose: true,
      btnProps: {
        type: 'primary',
      },
      visible: (record) => record.status === 'Waiting',
      handler: async (record: any, loadData: any) => {
        // 直接调用 handleApprove 方法，并传入必要的参数
        await handleApprove(record, loadData, 'Approved');
      },
    },
    {
      key: 'rejected',
      label: '拒绝',
      expose: true,
      btnProps: {
        type: 'outline',
        status: 'danger',
      },
      visible: (record) => record.status === 'Waiting',
      handler: async (record: any, loadData: any) => {
        // 直接调用 handleApprove 方法，并传入必要的参数
        await handleApprove(record, loadData, 'Rejected');
      },
    },
  ],
  detailViewProps: {
    columns: 1,
  },
  fieldsMap: {
    applicationType: {
      displayProps: {
        toDisplay(val) {
          return applicationTypes[val] || '';
        },
      },
    },
    subject: {
      listProps: {
        columnWidth: 250,
      },
    },
    submitDirectly: {
      visibleInDetail: false,
    },
    submitRemark: {
      visibleInDetail: false,
    },
    operationRecords: {
      displayProps: {
        component: 'ListTableDisplay',
        columns: operationRecordsColumns,
      },
    },
    applyReason4TourGuideVo: {
      visibleInDetail: (record) => isShowByType(record, 'TourGuide'),
      displayProps: {
        component: 'ListTableDisplay',
        columns: applyReasonColumns,
      },
    },
    students: {
      visibleInDetail: (record) => isShowByType(record, 'TourGuide'),
      displayProps: {
        detailSpan: 2,
        component: defineAsyncComponent(() => import('@repo/components/tourGuide/studentSimpleList.vue')),
      },
      /*      displayProps: {
        component: 'ListTableDisplay',
        columns: [
          {
            label: '学生姓名',
            key: 'studentName',
          },
          {
            label: '性别',
            key: 'gender',
          },
          {
            label: '学生编号',
            key: 'symbol',
          },
        ],
      }, */
    },

    problemDescription: {
      visibleInDetail: (record) => isShowByType(record, 'TourGuide'),
    },
    schoolOpinion: {
      visibleInDetail: (record) => isShowByType(record, 'TourGuide'),
    },
    application: {
      visibleInDetail: (record) => isShowByType(record, 'D2dService'),
    },
    student: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/components/student/studentDetailButton.vue')),
      },
      visibleInDetail: (record) => isShowByType(record, 'D2dService'),
    },
    studentSchool: {
      visibleInDetail: (record) => isShowByType(record, 'D2dService'),
    },
    attachments: {
      visibleInDetail: (record) => isShowByType(record, 'D2dService'),
      displayProps: {
        component: 'ListTableDisplay',
        columns: [
          {
            label: '类型',
            key: 'type',
          },
          {
            label: '附件',
            key: 'attachments',
            inputWidget: 'uploadInput',
            inputWidgetProps: {
              draggable: false,
              multiple: true,
            },
            displayProps: {
              component: 'AttachmentsPreviewDisplay',
            },
          },
        ],
      },
    },
  },
};

export default {
  verify,
};
