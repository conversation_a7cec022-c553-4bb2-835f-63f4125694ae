import { CustomSchema } from '@repo/infrastructure/types';
import useCommonStore from '@repo/infrastructure/utils/store';
import { useUserMenuStore } from '@repo/infrastructure/store';
import { defineAsyncComponent } from 'vue';

const diagnosisInputColumns = [
  {
    key: 'planGuideDate',
    label: '计划日期',
    inputWidget: 'dateInput',
    listProps: { columnWidth: 120 },
    inputWidgetProps: {
      placeholder: '请选择计划日期',
    },
  },
  {
    key: 'timeRange',
    label: '课时',
    inputWidget: 'selectInput',
    inputWidgetProps: {
      placeholder: '请选择课时',
      allowSearch: true,
      options: [
        { label: '全天', value: -1 }, // 0 不行
        { label: '上午', value: 1 },
        { label: '下午', value: 2 },
      ],
    },
    listProps: { columnWidth: 100 },
  },
];

const conventionalTeacher: CustomSchema = {
  api: '/resourceCenter/normalTourGuidePlan',
  detailViewProps: {
    columns: 1,
  },
  fieldsMap: {
    teacher: {
      labelField: 'name',
      valueField: 'id',
      inputWidget: 'selectInput',
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
      inputWidgetProps: {
        placeholder: '请选择巡回指导老师',
        allowSearch: true,
        async getOptions() {
          const menuStore = useUserMenuStore();
          const menuInfo = menuStore.getCurrentMenuInfo();
          const teacherStore = useCommonStore({
            api: '/org/companyUser/allTeachers',
            queryParams: {
              pageSize: 999,
              orgNature: menuInfo?.app?.label,
            },
          });
          const list = await teacherStore.getOptions();
          return list;
        },
        valueType: (value: any, computedOptions: any[]) => {
          const selected = computedOptions.find((item) => item.raw?.value === value);
          if (!selected) {
            return undefined;
          }
          return {
            id: selected.raw?.value,
            name: selected.raw?.label,
          };
        },
      },
    },
    major: {
      inputWidget: 'textareaInput',
      inputWidgetProps: {
        maxLength: 999,
        rows: 2,
      },
      displayProps: {
        detailSpan: 2,
      },
    },
    planTimes: {
      required: true,
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisInputColumns,
      },
      displayProps: {
        /* component: 'ListTableDisplay',
        columns: diagnosisInputColumns, */
        component: defineAsyncComponent(() => import('@repo/components/conventionalTeacher/planTimesDisplay.vue')),
      },
      startTime: {
        inputWidget: 'dateInput',
        inputWidgetProps: {
          placeholder: '请选择开始日期',
        },
      },
      endTime: {
        inputWidget: 'dateInput',
        inputWidgetProps: {
          placeholder: '请选择结束日期',
        },
      },
    },
  },
};

export default {
  conventionalTeacher,
};
