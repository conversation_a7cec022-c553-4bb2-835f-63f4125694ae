import { CustomSchema } from '@repo/infrastructure/types';
import { defineAsyncComponent } from 'vue';

const conferenceRecord: CustomSchema = {
  api: '/resourceCenter/conferenceRecord',
  fieldsMap: {
    coordinate: {
      displayProps: {
        toDisplay: (value: any) => {
          return value?.formattedAddress;
        },
      },
    },
    participant: {
      displayProps: {
        component: defineAsyncComponent(() => import('@repo/ui/components/data-display/components/avatarDisplay.vue')),
        mode: 'capsule',
      },
    },
    participantName: {
      key: 'participantName',
      label: '外部参会人员',
    },
    branchOfficeName: {
      displayProps: {
        toDisplay: (value: any) => {
          return value || '';
        },
      },
    },
  },
};

export default { conferenceRecord };
