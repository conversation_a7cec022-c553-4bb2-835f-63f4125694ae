import { CustomSchema } from '@repo/infrastructure/types';
import { Message } from '@arco-design/web-vue';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { useUserMenuStore } from '@repo/infrastructure/store';
import { ref } from 'vue';

const PaperCompetitionSchema: CustomSchema = {
  api: '/resourceCenter/evaluationCriterion',
  modalEdit: true,
  quickSearchProps: {
    enabled: true,
    placeholder: '按名称/描述搜索',
    fields: ['name', 'description'],
  },
  listViewProps: {
    rowActionWidth: 180,
    searchType: 'column',
  },
  rowActions: [
    {
      key: 'view',
      visible: false,
    },
    {
      key: 'edit',
      visible: false,
    },
    {
      key: 'viewScores',
      label: '查看评分',
      expose: true,
      handler() {},
    },
    {
      key: 'reviseCriteria',
      label: '指标修订',
      handler() {},
      visible(record) {
        return !record.enabled;
      },
    },
    {
      key: 'viewStatistics',
      label: '查看统计',
      handler() {},
    },
    {
      key: 'clone',
      label: '克隆一份',
      handler() {},
      visible: (record: any) => {
        return record?.canEdit;
      },
    },
    {
      key: 'experts',
      label: '评审专家',
      handler() {},
      visible(record) {
        return false;
      },
    },
    {
      key: 'assessmentLink',
      label: '获取评审链接',
      handler() {},
      visible(record) {
        return false;
      },
    },
    {
      key: 'specify',
      label: '指定类型',
      handler() {},
      visible(record) {
        const route = useRoute();
        const menuStore = useUserMenuStore();
        const menuInfo = ref<any>(menuStore.getCurrentMenuInfo(route));
        return menuInfo.value?.app?.key === 'center';
      },
    },
  ],
  fieldsMap: {
    name: {
      // unique: true,
      displayProps: {
        detailSpan: 2,
      },
    },
    type: {
      visibleInTable: false,
      visibleInDetail: false,
      visibleInForm: false,
    },
    description: {},
    cbName: {
      visibleInForm: false,
    },
    mbName: {
      visibleInForm: false,
    },
    startTime: {
      defaultValue: dayjs().format('YYYY-MM-DD'),
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    endTime: {
      defaultValue: dayjs().format('YYYY-MM-DD'),
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    enabled: {
      inputWidgetProps: {
        colSpan: 4,
      },
      displayProps: {
        detailSpan: 2,
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: (record: any) => {
            return record?.canEdit;
          },
          confirmMessage: (val) => `确定要${val ? '启用' : '停用'}这个指标吗？`,
          handler: async (val, record) => {
            Message.info(`正在${val ? '启用' : '停用'}... 请稍后`);
            try {
              // 修改指标的启用状态
              const { data } = await request(`/resourceCenter/evaluationCriterion/${record.id}`, {
                data: {
                  ...record,
                  enabled: val,
                },
                method: 'PUT',
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });

              Message.clear();
              Message.success(`${val ? '启用' : '停用'}成功`);

              return val;
            } catch (e) {
              return false;
            }
          },
        },
      },
    },
    criterionList: {
      visibleInForm: false,
      visibleInTable: false,
      displayProps: {
        detailSpan: 2,
        component: 'ListTableDisplay',
        columns: [
          { key: 'criterion', label: '评分标准' },
          { key: 'recommendScore', label: '推荐分值' },
          { key: 'sort', label: '排序' },
        ],
      },
    },
    experts: {
      visibleInForm: false,
      visibleInTable: false,
      displayProps: {
        detailSpan: 2,
        component: 'ListTableDisplay',
        columns: [
          { key: 'name', label: '专家姓名' },
          { key: 'mobile', label: '手机号码' },
          { key: 'email', label: '邮箱' },
          { key: 'organization', label: '单位' },
          { key: 'title', label: '职务' },
          { key: 'remark', label: '备注' },
        ],
      },
    },
  },
};

export default { PaperCompetitionSchema };
