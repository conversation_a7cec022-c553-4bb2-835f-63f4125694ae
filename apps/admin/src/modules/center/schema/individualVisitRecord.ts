import { CustomSchema } from '@repo/infrastructure/types';

const individualVisitRecord: CustomSchema = {
  api: '/resourceCenter/individualVisitFollowUp',
  fieldsMap: {
    individualVisit: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },

    questions: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 2,
      },
    },
    advice: {
      inputWidget: 'textareaInput',
      displayProps: {
        detailSpan: 2,
      },
    },
  },
};

export default { individualVisitRecord };
