import { CustomSchema } from '@repo/infrastructure/types';

const AssetInfoSchema: CustomSchema = {
  api: '/asset/assetInfo',
  modalEdit: true,
  quickSearchProps: {
    enabled: true,
    placeholder: '按编码/名称/类型搜索',
    fields: ['name', 'symbol', 'propertyType'],
  },
  listViewProps: {
    searchType: 'column',
  },
  rowActions: [
    {
      key: 'adjustInventory',
      label: '调整库存',
      icon: 'icon-storage',
      handler: () => {},
    },
  ],
  fieldsMap: {
    coverImage: {
      inputWidget: 'coverUploadInput',
      displayProps: {
        component: 'ImagePreviewInput',
      },
    },
    unit: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['个', '本', '件', '台', '套'],
        allowCreate: true,
        allowClear: true,
      },
    },
    propertyType: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: ['图书', '器材'],
        allowCreate: true,
        allowClear: true,
      },
      listProps: {
        columnWidth: 150,
        filterable: true,
      },
    },
    inventory: {
      updatable: false,
    },
    symbol: {
      unique: true,
      inputWidgetProps: {
        placeholder: '请输入编码，如不填写则自动生成',
      },
    },
    name: {
      unique: true,
    },
  },
};

export default { AssetInfoSchema };
