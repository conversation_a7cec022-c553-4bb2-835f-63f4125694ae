import { CustomSchema } from '@repo/infrastructure/types';
import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';
import { Message } from '@arco-design/web-vue';

const lessonSchedule: CustomSchema = {
  api: '/teacher/lessonSchedule',
  listViewProps: {
    rowActionWidth: 280,
  },
  rowActions: [
    {
      key: 'arrange',
      label: '安排课程',
      expose: true,
      icon: 'IconShrink',
      handler() {},
      btnProps: {
        type: 'outline',
      },
    },
    {
      key: 'copySchedule',
      label: '复制排课',
      expose: true,
      handler() {},
    },
    {
      key: 'edit',
      visible: false,
    },
  ],
  fieldsMap: {
    gradeLessons: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    gradeHours: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    period: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        options: getPeriodsList(),
      },
    },
    gradeConditions: {
      visibleInForm: false,
      visibleInDetail: false,
      visibleInTable: false,
    },
    createdBy: {
      key: 'createdBy',
      visibleInForm: false,
      label: '创建人',
      displayProps: {
        toDisplay(value) {
          return value?.name;
        },
      },
    },
    enabled: {
      visibleInForm: false,
      displayProps: {
        booleanDisplayOptions: {
          mode: 'switch',
          allowUpdate: true,
          confirmMessage(value) {
            return `确定要${value ? '启用' : '禁用'}该课表吗？（同学期只能有一个启用的课表存在）`;
          },
          async handler(value, record) {
            try {
              await request(`/teacher/lessonSchedule/${value ? 'enable' : 'disable'}/${record.id}`, {
                method: 'PUT',
                baseURL: PROJECT_URLS.MAIN_PROJECT_API,
              });
              Message.success(`已${value ? '启用' : '禁用'}该课表`);
            } catch (e) {
              Message.error('操作失败');
            }
          },
        },
      },
    },
  },
};

export default { lessonSchedule };
