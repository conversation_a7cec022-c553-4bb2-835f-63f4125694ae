import { CustomSchema } from '@repo/infrastructure/types';
import { secondsFormatter } from '@repo/ui/components/utils/utils';

const competitionAssessmentResult: CustomSchema = {
  api: '/paper/competitionEntryAssessmentResult',
  fieldsMap: {
    expertName: {
      listProps: {
        columnWidth: 130,
      },
      label: '评审专家',
      key: 'expertName',
    },
    expertMobile: {
      listProps: {
        columnWidth: 150,
      },
      label: '联系方式',
      key: 'expertMobile',
    },
    expertOrg: {
      label: '单位',
      key: 'expertOrg',
    },
    totalScore: {
      listProps: {
        columnWidth: 100,
      },
    },
    submitStatus: {
      listProps: {
        columnWidth: 100,
      },
    },
    timeSpend: {
      displayProps: {
        toDisplay: (value: number) => {
          return value ? secondsFormatter(value) : '-';
        },
      },
    },
  },
};

export default { competitionAssessmentResult };
