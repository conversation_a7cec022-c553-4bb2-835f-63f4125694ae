import { CustomSchema } from '@repo/infrastructure/types';
import { useUserStore } from '@repo/infrastructure/store';
import { usePrompt } from '@repo/ui/components';
import api from '@repo/infrastructure/openapi';
import { Message } from '@arco-design/web-vue';

const resourceCourseSchema: CustomSchema = {
  api: '/student/transfer',
  fieldsMap: {
    status: {
      listProps: {
        columnWidth: 100,
      },
    },
    auditor: {
      listProps: {
        columnWidth: 100,
      },
    },
    applier: {
      listProps: {
        columnWidth: 100,
      },
    },
    studentName: {
      listProps: {
        columnWidth: 100,
      },
    },
    auditTime: {
      listProps: {
        columnWidth: 120,
      },
      displayProps: {
        format: 'YYYY-MM-DD',
      },
    },
    createdAt: {},
  },

  rowActions: [
    {
      key: 'edit',
      visible: false,
    },
    {
      key: 'delete',
      visible: false,
    },
    {
      key: 'approve',
      label: '同意接收',
      visible: (record: any) => {
        const userStore = useUserStore();
        const { toBranchOfficeId } = record;
        const allDescendantIdsWithSelf = userStore.userInfo?.branchOffice?.allDescendantIdsWithSelf || [];
        return (
          (userStore.isAuthorized('admin_student:transfer:verify') ||
            userStore.isAuthorized('teacher_student:transfer:verify')) &&
          record.status === 'pending' &&
          allDescendantIdsWithSelf.indexOf(toBranchOfficeId) >= 0
        );
      },
      async handler(record: any, loadData) {
        const { prompt } = usePrompt();
        const val = await prompt({
          title: `确定同意${record.studentName}的转学申请吗？`,
          placeholder: '请输入同意转入备注',
          inputWidget: 'textarea',
        });

        await api.studentTransferController.studentTransferOperate({
          id: record.id,
          status: 'agreed' as any,
          remark: val,
        });
        Message.success('操作成功');
        await loadData();
      },
    },
    {
      key: 'refuse',
      label: '拒绝',
      visible: (record: any) => {
        const userStore = useUserStore();
        const { toBranchOfficeId } = record;
        const allDescendantIdsWithSelf = userStore.userInfo?.branchOffice?.allDescendantIdsWithSelf || [];
        return (
          (userStore.isAuthorized('admin_student:transfer:verify') ||
            userStore.isAuthorized('teacher_student:transfer:verify')) &&
          record.status === 'pending' &&
          allDescendantIdsWithSelf.indexOf(toBranchOfficeId) >= 0
        );
      },
      async handler(record: any, loadData) {
        const { prompt } = usePrompt();
        const val = await prompt({
          title: `确定拒绝${record.studentName}的转学申请吗？`,
          placeholder: '请输入拒绝转入备注',
          inputWidget: 'textarea',
        });

        await api.studentTransferController.studentTransferOperate({
          id: record.id,
          status: 'refused' as any,
          remark: val,
        });
        Message.success('操作成功');
        await loadData();
      },
    },
    {
      key: 'cancel',
      label: '撤销申请',
      visible: (record: any) => {
        const userStore = useUserStore();
        const { fromBranchOfficeId } = record;
        const allDescendantIdsWithSelf = userStore.userInfo?.branchOffice?.allDescendantIdsWithSelf || [];
        return (
          userStore.id === record.applierId &&
          record.status === 'pending' &&
          allDescendantIdsWithSelf.indexOf(fromBranchOfficeId) >= 0
        );
      },
      async handler(record: any, loadData) {
        await api.studentTransferController.studentTransferOperate({
          id: record.id,
          status: 'canceled' as any,
          remark: '',
        });
        Message.success('操作成功');
        await loadData();
      },
    },
  ],

  listViewProps: {
    visibleColumns: ['status', 'studentName', 'fromSchool', 'toSchool', 'applier', 'auditor', 'auditTime'],
  },

  detailViewProps: {
    showOthers: false,
    fieldsGrouping: [
      {
        label: '转学信息',
        fields: ['studentName', 'fromSchool', 'toSchool'],
      },
      {
        label: '申请信息',
        fields: ['applier', 'createdAt', 'remark'],
      },
      {
        label: '审核信息',
        fields: ['auditor', 'auditTime', 'auditRemark'],
      },
    ],
  },
};

export default { courseSchema: resourceCourseSchema };
