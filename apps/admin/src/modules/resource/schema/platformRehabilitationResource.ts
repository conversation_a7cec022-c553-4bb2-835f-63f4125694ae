import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';

const availableTypes = [
  { value: 'Company', label: '教师端' },
  { value: 'Guardian', label: '家长端' },
];

const prrSchema: CustomSchema = {
  api: '/rehabilitationLibrary/platformRehabilitationResource',
  listViewProps: {
    showIndex: false,
  },
  rowActions: [
    {
      key: 'pushToClient',
      label: '选择可用端',
      multiple: true,
      handler(record: any) {},
    },
  ],
  fieldsMap: {
    attachments: {
      inputWidget: 'uploadInput',
      inputWidgetProps: {
        limit: 8,
        showFileList: true,
        multiple: true,
      },
      displayProps: {
        component: 'AttachmentsPreviewDisplay',
        detailSpan: 2,
      },
    },
    category: {
      inputWidget: 'treeSelectInput',
      treeSourceApi: '/rehabilitationLibrary/platformRehabilitationResourceCategory',
      inputWidgetProps: {
        formatValue(val) {
          return {
            id: val?.id || val,
            // ...val,
          };
        },
        treeSourceApiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
      },
    },
    thumb: {
      inputWidget: 'coverUploadInput',
      displayProps: {
        component: 'ImagePreviewDisplay',
      },
    },
    availableTypes: {
      inputWidget: 'selectInput',
      key: 'availableTypes',
      label: '可用端',
      inputWidgetProps: {
        multiple: true,
        options: availableTypes,
      },
      displayProps: {
        toDisplay: (val) => {
          const selectedOptions = availableTypes.filter((option) => val?.includes(option.value));
          return selectedOptions.map((option) => option.label).join(', ');
        },
      },
    },
  },
};

export default { prrSchema };
