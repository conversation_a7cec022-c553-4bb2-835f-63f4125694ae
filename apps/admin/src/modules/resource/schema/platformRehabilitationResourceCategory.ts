import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';

const prrcSchema: CustomSchema = {
  api: '/rehabilitationLibrary/platformRehabilitationResourceCategory',
  listViewProps: {
    showIndex: false,
  },
  fieldsMap: {
    children: {
      visibleInForm: false,
      visibleInTable: false,
      visibleInDetail: false,
    },
    leaf: {
      visibleInForm: false,
      visibleInGrid: false,
      visibleInDetail: false,
    },
    hasChildren: {
      visibleInForm: false,
      visibleInGrid: false,
      visibleInDetail: false,
    },
    allDescendantIds: {
      visibleInForm: false,
      visibleInGrid: false,
      visibleInDetail: false,
    },
    allDescendantIdsWithSelf: {
      visibleInForm: false,
      visibleInGrid: false,
      visibleInDetail: false,
    },
    sort: {
      defaultValue: 99,
    },
    parentId: {
      label: '上级分类',
      inputWidget: 'treeSelectInput',
      treeSourceApi: '/rehabilitationLibrary/platformRehabilitationResourceCategory',
      inputWidgetProps: {
        treeSourceApiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
      },
    },
  },
};

export default { prrcSchema };
