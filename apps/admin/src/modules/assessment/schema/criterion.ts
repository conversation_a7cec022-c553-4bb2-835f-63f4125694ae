import { CustomSchema } from '@repo/infrastructure/types';
import { PROJECT_URLS } from '@repo/env-config';
import { defineAsyncComponent } from 'vue';
import { useRoute } from 'vue-router';
import { useUserMenuStore } from '@repo/infrastructure/store';
import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';

const rawPermissions = [
  'specialSchool:assessment:criterion:',
  'kindergarten:assessment:criterion:',
  'compulsoryEducation:assessment:criterion:',
  'vocationalEducation:assessment:criterion:',
  'institution:assessment:criterion:',
];

const updatePermissions = rawPermissions.map((item) => `${item}edit`);
const diagnosisTypeColumns = [
  {
    key: 'name',
    label: '诊断类型',
  },
];

const customCriterion: CustomSchema = {
  api: '/evaluation/customCriterion',
  permissions: {
    create: rawPermissions.map((item) => `${item}create`),
    update: updatePermissions,
    delete: rawPermissions.map((item) => `${item}delete`),
  },
  rowActions: [
    {
      label: '批量导出',
      key: 'batchExport',
      icon: 'icon-export',
      handler() {},
    },
    {
      key: 'importDetail',
      label: '导入量表明细',
      permNode: updatePermissions,
      visible(item) {
        return !item.type || item.type === 'Criterion';
      },
      handler() {},
    },
    {
      key: 'adjustDetail',
      label: '调整量表明细',
      permNode: updatePermissions,
      visible(item) {
        return !item.type || item.type === 'Criterion';
      },
      handler() {},
    },
  ],
  fieldsMap: {
    schoolCourseId: {
      inputWidget: 'selectInput',
      inputWidgetProps: {
        getOptions: async () => {
          const store = useSchoolCourseStore();
          const raw = await store.getSchoolCourses(true);
          return raw.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        },
      },
      displayProps: {
        toDisplay: async (val) => {
          const store = useSchoolCourseStore();
          const raw = await store.getSchoolCoursesMap();
          return raw[val]?.name;
        },
      },
    },
    category: {
      dataType: 'Foreign',
      inputWidgetProps: {
        valueType: (val) => {
          return { id: val };
        },
      },
      foreignField: {
        api: '/evaluation/evaluationCategory',
        preload: true,
        apiBaseUrl: PROJECT_URLS.MAIN_PROJECT_API,
        queryParams: () => {
          const route = useRoute();
          const menuStore = useUserMenuStore();
          return {
            orgNature: menuStore.getCurrentMenuInfo(route).app?.label,
          };
        },
      },
    },
    orgNature: {
      key: 'orgNature',
      visibleInForm: false,
    },
    maxScore: {
      key: 'maxScore',
      listProps: {
        columnWidth: 100,
      },
    },
    description: {
      displayProps: {
        detailSpan: 2,
      },
    },
    usedStudentCount: {
      key: 'usedStudentCount',
      listProps: {
        columnWidth: 100,
      },
      visibleInForm: false,
    },
    scores: {
      key: 'scores',
      inputWidget: defineAsyncComponent(() => import('@/views/manage/components/assessment/criterionScoresEdit.vue')),
      inputWidgetProps: {
        dynamicVisible: (record) => {
          return record.type === 'Criterion';
        },
      },
      displayProps: {
        detailSpan: 2,
        dynamicVisible: (record) => {
          return record.type === 'Criterion';
        },
        component: defineAsyncComponent(() => import('@/views/manage/components/assessment/criterionScoresView.vue')),
      },
    },
    diagnosisTypes: {
      key: 'diagnosisTypes',
      inputWidget: 'listTableInput',
      inputWidgetProps: {
        colSpan: 24,
        columns: diagnosisTypeColumns,
        dynamicVisible: (record) => {
          return record.type === 'Diagnosis';
        },
      },
      displayProps: {
        detailSpan: 2,
        dynamicVisible: (record) => {
          return record.type === 'Diagnosis';
        },
        component: 'ListTableDisplay',
        columns: diagnosisTypeColumns,
      },
    },
  },
};

export default {
  customCriterion,
};
