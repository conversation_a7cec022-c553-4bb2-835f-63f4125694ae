<template>
  <a-config-provider :locale="locale" :size="componentSize">
    <router-view v-if="ready" />
  </a-config-provider>
</template>

<script lang="ts" setup>
  import { computed, onMounted, Ref, ref } from 'vue';
  import zhCN from '@arco-design/web-vue/es/locale/lang/zh-cn';
  import { useAppStore } from '@/common/store';
  import { isLogin } from '@repo/infrastructure/auth';
  import {
    useSchemaStore,
    useNotificationStore,
    useConfigureStore,
    useTeacherStore,
    useUserMenuStore,
  } from '@repo/infrastructure/store';
  import getUnifiedSession from '@/common/store/unifiedSession';
  import { PROJECT_URLS } from '@repo/env-config';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import { clientRedirectTo, isElectron } from '@repo/infrastructure/electron';
  import useMenuRenameStore from '@repo/infrastructure/store/menuRenameStore';

  const appStore = useAppStore();
  const componentSize: Ref<'mini' | 'small' | 'medium' | 'large'> = ref<any>(
    appStore.componentSize as 'mini' | 'small' | 'medium' | 'large',
  );
  // const { currentLocale } = useLocale();
  const currentLocale = ref<any>('zh-CN');
  const locale = computed(() => {
    switch (currentLocale.value) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
        return zhCN;
      default:
        return zhCN;
    }
  });
  const ready = ref<any>(false);

  // changeLocale(res.defaultLocale || defaultLocale, res.localeMessages);
  // const userStore = useUserStore();
  // userStore.setInfo(res.currentUser as Partial<any>);
  appStore.updateSettings(null);
  onMounted(async () => {
    await getUnifiedSession();
    if (isLogin()) {
      const schemaStore = useSchemaStore();
      const notificationStore = useNotificationStore();
      const configureStore = useConfigureStore();
      const menuRenameStore = useMenuRenameStore();

      await Promise.all([
        schemaStore.fetchSchemaList(),
        notificationStore.refresh(),
        configureStore.load(),
        menuRenameStore.loadData(),
      ]);

      ready.value = true;
    } else if (isElectron) {
      clientRedirectTo('teacher', '');
    } else {
      window.location.href = `${PROJECT_URLS.MAIN_PROJECT}/?redirectTo=course`;
    }
  });

  // useConfigure().then(async (res) => {
  //
  // });
</script>

<style lang="scss">
  ::-webkit-scrollbar {
    height: 1px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #d3d3d3;
    border-radius: 10px;
  }

  ::-webkit-scrollbar-track {
    background-color: transparent; /* 可设置为其他背景色 */
  }

  /* 针对 Firefox 浏览器 */
  * {
    scrollbar-width: thin; /* 滚动条变细 */
    scrollbar-color: #979797 transparent; /* 滚动条颜色和轨道背景色 */
  }
</style>
