<script setup lang="ts">
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useUserStore } from '@repo/infrastructure/store';

  const tableRef = ref<any>(null);
  const schema = ref<any>();
  const { userInfo } = useUserStore();

  // 状态正常 && settled true
  const queryParams = ref({
    filters: JSON.stringify([{ property: 'specialEduCommitteeId', operator: 'NotNull' }]),
    orgNature: 'ALL',
  });

  const visibleColumns = ['symbol', 'name', 'gender', 'age', 'birthday', 'disorders', 'fusionSchool', 'gradeClass'];

  const hiddenActionKeys = ['archive', 'specialCommittee', 'graduation', 'setGradeClass', 'updateStatus'];
  onMounted(async () => {
    schema.value = await <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.getInstanceByDs('/resourceRoom/student', {
      fieldsOverride: {
        fusionSchool: {
          label: '安置学校',
        },
      },
      schemaOverride: {
        importable: {
          enabled: false,
        },
      } as any,
    });
    schema.value.rowActions.forEach((item: any) => {
      if (hiddenActionKeys.includes(item.key)) {
        item.visible = false;
      }
    });
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <table-action
        v-if="tableRef"
        class="flex-1"
        :schema="schema"
        :visible-components="['refresh', 'search', 'recycleBin', 'quickSearch']"
        :table="tableRef"
        component-size="mini"
        :show-layout-manage="false"
      >
        <template #title>
          <slot name="title">已安置学生信息库</slot>
        </template>
      </table-action>
      <slot name="table-action-append"></slot>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      size="mini"
      module-path="/manage/specialEduCommittee/student"
      :default-query-params="queryParams"
      :visible-columns="visibleColumns"
    >
    </crud-table>
  </a-card>
</template>

<style scoped lang="scss"></style>
