<script setup lang="ts">
  import dataMonitoringFiltersCard from '@/views/manage/components/specialEduCommittee/dataMonitoringFiltersCard.vue';
  import { computed, onMounted, ref, provide, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { getCurrentPeriod } from '@repo/infrastructure/utils/scaffolds';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import { getSubmittableRowClass } from '@repo/components/utils/collaborate';

  const customizeRef = ref<any>();

  const modules = [
    {
      label: '安置一致性监测',
      key: 'consistency',
      children: [
        { label: '一致', key: 'consistent' },
        { label: '不一致', key: 'inconsistent' },
      ],
    },
    {
      label: '家长安置意向',
      key: 'parentalPreference',
      children: [
        {
          label: '特殊教育学校',
          key: 'specialSchool',
        },
        {
          label: '普通学校',
          key: 'regularSchool',
        },
        {
          label: '送教上门',
          key: 'homeDelivery',
        },
        {
          label: '其他',
          key: 'other',
        },
      ],
    },
    {
      label: '专委会安置建议',
      key: 'committeeRecommendation',
      children: [
        {
          label: '特殊教育学校',
          key: 'specialSchool',
        },
        {
          label: '普通学校',
          key: 'regularSchool',
        },
        {
          label: '送教上门',
          key: 'homeDelivery',
        },
        {
          label: '其他',
          key: 'other',
        },
      ],
    },
    {
      label: '安置结果',
      key: 'placementResult',
      children: [
        { label: '特殊教育学校', key: 'specialSchool' },
        { label: '普通学校', key: 'regularSchool' },
        { label: '送教上门', key: 'homeDelivery' },
        { label: '其他', key: 'other' },
      ],
    },
  ];

  const commonColumns = [
    { title: '序号', slotName: 'num', width: 100, align: 'center' },
    { dataIndex: 'student.name', title: '学生' },
    { dataIndex: 'student.age', title: '年龄' },
    { dataIndex: 'student.gender', title: '性别' },
    { dataIndex: 'student.disorders', title: '障碍类型' },
    // { dataIndex: 'submitStatus', title: '状态', slotName: 'submitStatus' },
  ];
  const columns = [
    ...commonColumns,
    { dataIndex: 'createdDate', title: '时间', slotName: 'createdDate' },
    { dataIndex: 'cbName', title: '老师' },
    { title: '操作', slotName: 'operate', width: 220, align: 'center' },
  ];

  const params = ref<any>({
    statisticsType: 'placement',
    current: 1,
    page: 1,
    pageSize: 20,
    period: getCurrentPeriod(),
  });
  provide('RecordDetail', RecordDetail);
  const total = ref(0);
  const pagination = computed(() => {
    return {
      current: params.value.current,
      total: total.value,
      pageSize: params.value.pageSize,
      page: params.value.page,
      showTotal: true,
      pageSizeOptions: [2, 20, 30, 50, 100, 200],
      showPageSize: true,
      size: 'small',
    };
  });

  const data = ref();
  const displayData = ref({
    consistency: {
      consistent: 0,
      inconsistent: 0,
    },
    parentalPreference: {
      specialSchool: 0,
      regularSchool: 0,
      homeDelivery: 0,
      other: 0,
    },
    committeeRecommendation: {
      specialSchool: 0,
      regularSchool: 0,
      homeDelivery: 0,
      other: 0,
    },
    placementResult: computed(() => {
      return data.value?.placementResult;
    }),
  });

  const handleResult = () => {
    const fields = customizeRef.value?.exposed?.fields?.placementReportStatistics;
    if (!fields) return {};

    const { parentIntention, expertIntention } = fields;

    const mapOptionsToDisplay = (options: any, target: any, sourceData: any[]) => {
      if (!options || !sourceData) return;

      Object.values(options).forEach((option: any) => {
        const value = sourceData.find((item) => item?.itentionType === option.value)?.number ?? 0;

        // 需要修改...
        if (option.label.includes('特殊')) target.specialSchool = value;
        else if (option.label.includes('普通')) target.regularSchool = value;
        else if (option.label.includes('上门')) target.homeDelivery = value;
        else if (option.label.includes('其他')) target.other = value;
      });
    };

    const categories = ['specialSchool', 'regularSchool', 'homeDelivery', 'other'];

    mapOptionsToDisplay(parentIntention?.options, displayData.value.parentalPreference, data.value?.parentIntention);
    mapOptionsToDisplay(
      expertIntention?.options,
      displayData.value.committeeRecommendation,
      data.value?.expertIntention,
    );

    const parent = displayData.value.parentalPreference;
    const expert = displayData.value.committeeRecommendation;

    displayData.value.consistency.inconsistent = categories.reduce(
      (sum, key) => sum + Math.abs((parent[key] ?? 0) - (expert[key] ?? 0)),
      0,
    );

    const totalNum = categories.reduce((sum, key) => sum + (parent[key] ?? 0), 0);
    displayData.value.consistency.consistent = totalNum - displayData.value.consistency.inconsistent;

    return {};
  };

  const loadData = async () => {
    Message.loading('数据加载中...');
    // 获取统计需要的字段路径
    const fields = customizeRef.value?.exposed?.fields?.placementReportStatistics;
    if (!fields) return;
    try {
      const { data: res } = await request('/statistics/specialCommitteeStatistics', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        params: {
          parentIntention: fields?.parentIntention?.path || '',
          expertIntention: fields?.expertIntention?.path || '',
        },
        data: params.value,
      });
      data.value = res;
      total.value = res?.totalData || res.total;
      handleResult();
    } finally {
      Message.clear('top');
    }
  };
  const viewVisible = ref(false);
  const schema = ref();
  const currentRecord = ref();

  const handleView = async (record: any) => {
    currentRecord.value = record;
    viewVisible.value = true;
  };
  const handlePageChange = async (val: number) => {
    params.value.current = val;
    params.value.page = val;
    await loadData();
  };

  const handlePageSizeChange = async (val: number) => {
    params.value.pageSize = val;
    await loadData();
  };

  const handleSearch = async (val: any) => {
    params.value = { ...params.value, ...val };
    await loadData();
  };

  const handler = async (item: any, child: any) => {
    if (['consistency', 'placementResult'].includes(item.key)) {
      Message.info('暂不支持查看该分类');
      return;
    }
    // 自定义组件返回查询条件 || 根据item,child
    params.value.jsonObject = customizeRef.value.callMethodSafely('getJsonObject', item, child) || null; // {"intention": "1" } || 自定义组件返回查询条件
    await loadData();
  };

  onMounted(async () => {
    await loadData();
    schema.value = await SchemaHelper.getInstanceByDs('/specialCommittee/placementReport');
  });
  watch(
    () => customizeRef.value?.exposed?.fields,
    async (newVal) => {
      await loadData();
    },
  );
</script>

<template>
  <dataMonitoringFiltersCard
    :modules="modules"
    :data="displayData"
    :params="params"
    @handler="handler"
    @handle-search="handleSearch"
  />
  <div class="p-2">
    <a-table
      :columns="columns"
      :pagination="pagination"
      :data="data?.raw || []"
      :bordered="{ cell: true }"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template #num="{ rowIndex }">
        {{ rowIndex + 1 }}
      </template>
      <template #createdDate="{ record }">
        <span>{{ record.createdDate?.split(' ')[0] }}</span>
      </template>
      <template #operate="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button size="mini" @click="handleView(record)">查看</a-button>
        </div>
      </template>
    </a-table>
  </div>
  <a-modal v-model:visible="viewVisible" fullscreen>
    <template #title>
      <div>
        {{ currentRecord?.student.name + '安置报告' }}
      </div>
    </template>

    <!--这里统一暴露查询的字段，以及定义-->
    <!--special、normal、send、others-->
    <customize-component
      ref="customizeRef"
      v-model="currentRecord"
      :record="currentRecord"
      :schema="schema"
      module="SpecialCommitteeTeacherClient"
      page="View"
    >
      <template #default>
        <record-detail ref="recordDetailRef" :raw="currentRecord" :schema="schema" />
      </template>
    </customize-component>
  </a-modal>
</template>

<style scoped lang="scss"></style>
