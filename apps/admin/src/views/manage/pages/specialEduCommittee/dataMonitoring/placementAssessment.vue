<script setup lang="ts">
  import dataMonitoringFiltersCard from '@/views/manage/components/specialEduCommittee/dataMonitoringFiltersCard.vue';
  import { computed, ref, onMounted } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import Analysis from '@repo/components/assessment/analysis.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { Message } from '@arco-design/web-vue';
  import { getCurrentPeriod } from '@repo/infrastructure/utils/scaffolds';

  const bodyStyle = {
    height: '100%',
    overflow: 'auto',
  };
  const modules = [
    {
      label: '评估概况',
      key: 'overview',
      children: [
        { label: '总评估', key: 'total', color: -2 },
        { label: '已完成', key: 'finished', color: 31, params: { statisticsStatus: 'Enable' } },
        { label: '已归档', key: 'archived', color: 45, params: { submitStatus: 'Submitted' } },
        { label: '进行中', key: 'inProgress', color: 18, params: { submitStatus: 'Draft' } },
      ],
    },
    {
      label: '评估详情',
      key: 'details',
      children: [
        { label: '已评估', key: 'finished', color: 31 },
        { label: '未评估', key: 'inProgress', color: 4 },
      ],
    },
    {
      label: '评估进度',
      key: 'progress',
      children: [
        { label: '参与教师', key: 'teachersNum', color: 46 },
        { label: '量表分数', key: 'scaleNum', color: 61 },
      ],
    },
  ];
  const commonColumns = [
    { title: '序号', slotName: 'num', width: 100, align: 'center' },
    { dataIndex: 'student.name', title: '学生' },
    { dataIndex: 'student.disorders', title: '障碍类型' },
    { dataIndex: 'submitStatus', title: '状态', slotName: 'submitStatus' },
  ];
  const columns = [
    ...commonColumns,
    { dataIndex: 'type', title: '类型', slotName: 'type' },
    { dataIndex: 'criterionName', title: '量表', slotName: 'criterionName' },
    { dataIndex: 'timesLabel', title: '次数' },
    { dataIndex: 'evaluationDate', title: '时间' },
    { dataIndex: 'assessmentTeachers', title: '老师' },
    { title: '操作', slotName: 'operate', width: 220, align: 'center' },
  ];

  const defaultValue = {
    selectAll: -1,
  };
  const params = ref<any>({
    statisticsType: 'evaluate',
    current: 1,
    page: 1,
    pageSize: 20,
    period: getCurrentPeriod(),
  });
  const total = ref();
  const pagination = computed(() => {
    return {
      current: params.value.current,
      total: total.value,
      pageSize: params.value.pageSize,
      page: params.value.page,
      showTotal: true,
      pageSizeOptions: [2, 20, 30, 50, 100, 200],
      showPageSize: true,
      size: 'small',
    };
  });

  const data = ref();
  const loadData = async () => {
    Message.loading('数据加载中...');
    try {
      const { data: res } = await request('/statistics/specialCommitteeStatistics', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: params.value,
      });
      data.value = res;
      total.value = res?.totalData;
    } finally {
      Message.clear('top');
    }
  };
  const viewVisible = ref(false);
  const schema = ref();
  const currentRecord = ref();

  const handleView = async (record: any) => {
    currentRecord.value = record;
    viewVisible.value = true;
  };

  const handlePageChange = async (val: number) => {
    params.value.current = val;
    params.value.page = val;
    await loadData();
  };

  const handlePageSizeChange = async (val: number) => {
    params.value.pageSize = val;
    await loadData();
  };

  const handleSearch = async (val: any) => {
    params.value = { ...params.value, ...val };
    await loadData();
  };

  const handler = async (item: any, child: any) => {
    console.log(item, child);
    params.value.statisticsStatus = null;
    params.value.submitStatus = null;
    params.value = { ...params.value, ...child?.params };
    await loadData();
  };

  onMounted(async () => {
    await loadData();
    schema.value = await SchemaHelper.getInstanceByDs('/evaluation/customCriterionResult');
  });
</script>

<template>
  <dataMonitoringFiltersCard
    :modules="modules"
    :data="data"
    :params="params"
    @handler="handler"
    @handle-search="handleSearch"
  />
  <a-divider />
  <div class="p-2">
    <a-table
      :columns="columns"
      :pagination="pagination"
      :data="data?.raw || []"
      :bordered="{ cell: true }"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    >
      <template #num="{ rowIndex }">
        {{ rowIndex + 1 }}
      </template>
      <template #submitStatus="{ record }">
        <span>{{ record.submitStatus === 'Draft' ? '草稿' : '已提交' }}</span>
      </template>
      <template #type="{ record }">
        <span>{{ record.type === 'Criterion' ? '量表' : '诊断' }}</span>
      </template>
      <template #operate="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button size="mini" @click="handleView(record)">查看</a-button>
        </div>
      </template>
    </a-table>
  </div>
  <a-modal v-model:visible="viewVisible" fullscreen title="详情" :body-style="bodyStyle">
    <analysis
      v-if="viewVisible"
      v-model:visible="viewVisible"
      :result-id="currentRecord?.id"
      :current-times="currentRecord?.times"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
