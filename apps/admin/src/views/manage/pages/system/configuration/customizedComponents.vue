<script setup lang="ts">
  // import MonacoEditor from 'monaco-editor-vue3';
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import {
    CustomizeComponentClient,
    CustomizeComponentModule,
    CustomizeComponentPage,
  } from '@repo/infrastructure/customizeComponent/store';

  const loading = ref(false);

  type ModulePage = {
    name: string;
    module: CustomizeComponentModule;
    page: CustomizeComponentPage;
    key: string;
  };
  type Client = {
    client: CustomizeComponentClient;
    name: string;
  };

  const clients: Client[] = [
    { client: 'Web', name: '电脑端' },
    { client: 'UniAPP', name: '手机端' },
  ];

  const availableModulePages: Record<CustomizeComponentClient, ModulePage[]> = {
    Web: [
      { name: '送教计划-编辑', module: 'SendEducationPlan', page: 'Edit', key: 'SendEducationPlan-Edit' },
      { name: '送教计划-查看', module: 'SendEducationPlan', page: 'View', key: 'SendEducationPlan-View' },
      { name: '送教计划-打印', module: 'SendEducationPlan', page: 'Print', key: 'SendEducationPlan-Print' },
      { name: '送教记录-编辑', module: 'SendEducationRecord', page: 'Edit', key: 'SendEducationRecord-Edit' },
      { name: '送教记录-查看', module: 'SendEducationRecord', page: 'View', key: 'SendEducationRecord-View' },
      { name: 'IEP-编辑', module: 'IEP', page: 'Edit', key: 'IEP-Edit' },
      { name: 'IEP-查看', module: 'IEP', page: 'View', key: 'IEP-View' },
      { name: 'IEP-打印', module: 'IEP', page: 'Print', key: 'IEP-Print' },
      { name: 'IEP-导出', module: 'IEP', page: 'Export', key: 'IEP-Export' },
      { name: '学生-查看', module: 'Student', page: 'View', key: 'Student-View' },
      { name: '学生-编辑', module: 'Student', page: 'Edit', key: 'Student-Edit' },
      {
        name: '安置申请-查看',
        module: 'ResettlementApplication',
        page: 'View',
        key: 'ResettlementApplication-View',
      },
      {
        name: '安置报告-编辑',
        module: 'SpecialCommitteeTeacherClient',
        page: 'Edit',
        key: 'SpecialCommitteeTeacherClient-Edit',
      },
      {
        name: '安置报告-查看',
        module: 'SpecialCommitteeTeacherClient',
        page: 'View',
        key: 'SpecialCommitteeTeacherClient-View',
      },
      {
        name: '安置报告-打印',
        module: 'SpecialCommitteeTeacherClient',
        page: 'Print',
        key: 'SpecialCommitteeTeacherClient-Print',
      },
      {
        name: '学生安置信息-打印',
        module: 'SpecialCommitteeManagerClient',
        page: 'Print',
        key: 'SpecialCommitteeManagerClient-Print',
      },
      {
        name: '安置信息-批量导出',
        module: 'SpecialCommitteeManagerClient',
        page: 'PlacementBatchExport',
        key: 'SpecialCommitteeManagerClient-PlacementBatchExport',
      },
    ],
    UniAPP: [
      { name: '送教计划-查看', module: 'SendEducationPlan', page: 'View', key: 'SendEducationPlan-View' },
      { name: '送教记录-编辑', module: 'SendEducationRecord', page: 'Edit', key: 'SendEducationRecord-Edit' },
      { name: '送教记录-查看', module: 'SendEducationRecord', page: 'View', key: 'SendEducationRecord-View' },
      // { name: 'IEP-编辑', module: 'IEP', page: 'Edit', key: 'IEP-Edit' },
      { name: 'IEP-查看', module: 'IEP', page: 'View', key: 'IEP-View' },
      { name: '学生-安置申请提交', module: 'Student', page: 'ResettlementApply', key: 'Student-ResettlementApply' },
      {
        name: '学生-安置申请进度',
        module: 'Student',
        page: 'ResettlementApplyProgress',
        key: 'Student-ResettlementApplyProgress',
      },
      { name: '学生-查看', module: 'Student', page: 'View', key: 'Student-View' },
    ],
  };
  const activeClient = ref<CustomizeComponentClient>('Web');
  const currentPage = ref<ModulePage>(availableModulePages.Web[0]);

  const handleModulePageChange = (key: string) => {
    currentPage.value = availableModulePages[activeClient.value].find((mp) => mp.name === key);
  };

  const currentEditorRef = ref(null);
  const currentEditorId = computed(() => `code-editor-${activeClient.value}-${currentPage.value.key}`);

  const codesRawMap = ref<Record<string, any>>({});
  const editorOptions = {
    colorDecorators: true,
    lineHeight: 24,
    tabSize: 2,
    wordWrap: 'on',
  };

  const loadList = async () => {
    const { data } = await request('/resourceRoom/centralConfiguration/customizeComponents', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    codesRawMap.value = data.reduce((acc: Record<string, any>, item: any) => {
      const key = `${item.client}-${item.module}-${item.page}`;
      acc[key] = item.raw;
      return acc;
    }, {});
  };

  const handleSave = async () => {
    const lists = [];
    Object.keys(codesRawMap.value).forEach((key) => {
      const [client, module, page] = key.split('-');
      lists.push({
        client,
        module,
        page,
        raw: codesRawMap.value[key],
      });
    });

    loading.value = true;
    try {
      await request('/resourceRoom/centralConfiguration/customizeComponents', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: lists,
      });

      Message.success('保存成功');
    } finally {
      loading.value = false;
    }
  };

  const editClass =
    'flex-1 min-w-0 h-full resize-y resize-y h-[80vh] border-0 border-l-2 border-dashed border-slate-500';
  onMounted(async () => {
    await loadList();
  });
</script>

<template>
  <a-card>
    <a-tabs v-model:active-key="activeClient">
      <template #extra>
        <a-button size="mini" type="primary" :loading="loading" @click="handleSave">
          <template #icon>
            <IconSave />
          </template>
          保存
        </a-button>
      </template>
      <a-tab-pane v-for="c in clients" :key="c.client" :title="c.name">
        <a-tabs direction="vertical" @change="handleModulePageChange">
          <a-tab-pane
            v-for="modulePage in availableModulePages[c.client]"
            :key="modulePage.name"
            :title="modulePage.name"
          >
            <div v-if="activeClient === c.client && currentPage === modulePage" id="code-editor" />
            <!--<MonacoEditor-->
            <!--  v-model="codesRawMap[`${c.client}-${modulePage.module}-${modulePage.page}`]"-->
            <!--  theme="vs-dark"-->
            <!--  :options="editorOptions"-->
            <!--  language="html"-->
            <!--  :height="600"-->
            <!--/>-->
            <div class="flex w-full h-full">
              <!-- Textarea -->
              <a-textarea
                v-model="codesRawMap[`${c.client}-${modulePage.module}-${modulePage.page}`]"
                :textarea-attrs="{ rows: 20 }"
                :class="editClass"
                style="height: 80vh"
              >
              </a-textarea>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<style scoped lang="scss"></style>
