<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    modelValue: {
      type: Boolean,
      required: true,
    },
    role: {
      type: Object,
      required: true,
    },
    roleUsers: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['update:modelValue', 'ok']);
  const { loading, setLoading } = useLoading();

  const visible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });
  const allUsers = ref<any[]>([]);

  const selectedUsers = computed(() => {
    return (
      props.roleUsers?.map((item: any) => {
        return {
          value: item.id.toString(),
          label: item.name,
          avatar: item.avatar,
        };
      }) || []
    );
  });

  const selectedUsersId = ref<string[]>(selectedUsers.value.map((item) => item.value));

  const handleOk = async () => {
    setLoading(true);
    try {
      await request(`/org/sysRole/authorize`, {
        method: 'POST',
        data: selectedUsersId.value.map((id: string) => {
          return Number(id);
        }),
        params: {
          roleId: props.role.id,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('授权成功');
      emit('ok');
      emit('update:modelValue', false);
    } finally {
      setLoading(false);
    }
  };
  /* allUser没包含完selectUsers */
  const mergeUsersData = ref<any>();
  const loadAllUsers = async (keyword?: string) => {
    setLoading(true);
    try {
      const { data } = await request('/org/companyUser', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          'pageSize': 50,
          'name|mobile|username': `%${keyword || '_'}%`,
        },
      });
      allUsers.value = (data.items || []).map((item: any) => {
        return {
          value: item.id.toString(),
          label: item.name,
          username: item.username,
          avatar: item.avatar,
        };
      });

      // 合并两个数组
      const mergedArray = [...allUsers.value, ...selectedUsers.value];

      // 使用 Map 去重
      const map = new Map();
      mergedArray.forEach((item) => map.set(item.value, item)); // 使用 value 作为唯一标识
      mergeUsersData.value = [...map.values()];
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (keyword: string) => {
    await loadAllUsers(keyword);
  };

  const handleSelectChange = (selectedIds: any) => {
    selectedUsersId.value = selectedIds;
  };

  onMounted(async () => {
    await loadAllUsers();
  });
</script>

<template>
  <a-modal v-model:visible="visible" title="用户管理" :on-before-ok="handleOk" :ok-loading="loading">
    <a-spin :loading="loading">
      <a-transfer
        :data="mergeUsersData"
        :default-value="selectedUsersId"
        one-way
        show-search
        @search="handleSearch"
        @change="handleSelectChange"
      >
        <template #item="userInfo">
          <div class="flex gap-2 items-center">
            <avatar-display
              :user-card="true"
              :user-info="{ id: userInfo.value, name: userInfo.label, avatar: userInfo.avatar }"
              :size="25"
            />
            {{ userInfo.label }}
          </div>
        </template>
        <template #source-title="{ countTotal }">
          <div> 可授权用户 ({{ countTotal }}) </div>
        </template>

        <template #target-title="{ countTotal }">
          <div> 已授权 ({{ countTotal }}) </div>
        </template>
      </a-transfer>
    </a-spin>
  </a-modal>
</template>

<style scoped lang="less"></style>
