<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { UNIT_NATURES_OPTIONS } from '@repo/infrastructure/constants';

  const props = defineProps({
    modelValue: {
      type: Object,
    },
    visible: {
      type: Boolean,
    },
  });

  const emit = defineEmits(['update:modelValue', 'update:visible', 'ok']);
  const { loading, setLoading } = useLoading();

  // const data = ref<any>(props.modelValue || {});

  const data = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
  });

  const modalVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });

  const title = computed(() => (data.value.id ? '编辑岗位' : '新增岗位'));

  const handleCancel = () => {
    modalVisible.value = false;
    data.value = {};
    emit('update:modelValue', data.value);
  };

  const formRef = ref(null);
  const handleOk = async () => {
    const valid = await formRef.value?.validate();
    if (valid === undefined || Object.keys(valid).length === 0) {
      setLoading(true);
      try {
        const url = data.value.id ? `/org/sysRole/${data.value.id}` : '/org/sysRole';
        await request(url, {
          method: data.value.id ? 'PUT' : 'POST',
          data: data.value,
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        });
        Message.success('操作成功');
        // modalVisible.value = false;
        emit('update:modelValue', data.value);
        emit('ok');
        data.value = {};
        return true;
      } catch (e) {
        return false;
      } finally {
        setLoading(false);
      }
    }
    return false;
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    :title="title"
    :ok-loading="loading"
    :on-before-ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="data">
      <a-form-item label="岗位名称" name="name" field="name" :rules="{ required: true, message: '岗位名称不能为空' }">
        <a-input v-model.trim="data.name" />
      </a-form-item>
      <a-form-item label="岗位描述" name="description" field="description">
        <a-input v-model.trim="data.description" />
      </a-form-item>
      <a-form-item
        label="单位性质"
        name="unitNature"
        field="unitNature"
        :rules="{ required: true, message: '单位性质不能为空' }"
      >
        <a-select v-model="data.unitNature" :options="UNIT_NATURES_OPTIONS" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less"></style>
