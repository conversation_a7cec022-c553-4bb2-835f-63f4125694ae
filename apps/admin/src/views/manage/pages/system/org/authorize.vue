<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';
  import { menuService } from '@repo/infrastructure/data';
  import AntPathMatcher from '@maxbilbow/ant-path-matcher';
  import { Message } from '@arco-design/web-vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { UNIT_NATURES_MAP } from '@repo/infrastructure/constants';
  import UserManage from './authorize/userManage.vue';
  import RoleEdit from './authorize/roleEdit.vue';

  const adminPermNodes = menuService.adminAllMenus;
  const teacherPermNodes = menuService.teacherAllMenus;

  const currentRole = ref<any>({});
  const currentNode = ref<any>({});
  const activeTab = ref<any>('teacher');
  const allRoles = ref<any[]>([]);
  const roleUsers = ref<any[]>([]);
  const grantedPermissionSet = ref<any[]>([]);
  const editVisible = ref<any>(false);
  const editData = ref<any>({});
  const userManageVisible = ref<any>(false);

  const matcher = AntPathMatcher();
  const userStore = useUserStore();

  const perms = ref<any>({
    teacher: teacherPermNodes,
    admin: adminPermNodes,
  });

  const permsIndex = computed(() => {
    const result: Record<string, string[]> = {};
    Object.keys(perms.value).forEach((key) => {
      result[key] = perms.value[key]?.map((item) => item.key);
    });
    return result;
  });

  // 将perms中的节点递归扁平化处理，以permission作为key，节点本身作为value
  const permsFlat = computed(() => {
    const result: Record<string, any> = {};

    const catalogIndex = permsIndex.value[activeTab.value];
    const flatten = (nodes: any[]) => {
      nodes.forEach((node) => {
        const prefix = node.permission.split(':')[0];
        // if (!catalogIndex.includes(prefix)) {
        //   return;
        // }

        if (node.children) {
          flatten(node.children);
        } else {
          result[node.permission] = node;
        }
      });
    };
    Object.values(perms.value).forEach((nodes) => {
      flatten(nodes as menuService.UserMenu[]);
    });

    // sort by node.permission
    return Object.fromEntries(Object.entries(result).sort((a, b) => a[0].localeCompare(b[0])));
  });

  /**
   * 计算当前节点的选中或半选状态
   * 1. 如果当前节点的所有子节点都被选中，则当前节点被选中 1
   * 2. 如果当前节点的所有子节点都未被选中，则当前节点未被选中 0
   * 3. 如果当前节点的子节点有被选中的，有未被选中的，则当前节点为半选状态 2
   */
  const groupsCheckStatus = computed(() => {
    const result: Record<string, number> = {};

    Object.keys(permsFlat.value)?.forEach((permission: string) => {
      const group = permission.split(':')[0] as string;
      result[group] = result[group] || 0;
      const granted = currentRole.value.permissionCodes || [];
      const allChecked = granted.includes(`${group}:*`);
      const indeterminate =
        granted.filter((fp: string) => {
          return fp.startsWith(`${group}:`);
        }).length > 0;
      if (allChecked) {
        result[group] = 1;
      } else if (indeterminate) {
        result[group] = 2;
      }
    });

    return result;
  });

  const tabs = ref([
    { label: '教师端', key: 'teacher', nodes: perms.value.teacher },
    { label: '管理端', key: 'admin', nodes: perms.value.admin },
  ]);

  watch(
    () => activeTab.value,
    (tab) => {
      const node = tabs.value.find((t) => t.key === tab)?.nodes[0];
      if (node) {
        currentNode.value = node;
      }
    },
  );

  const { loading, setLoading } = useLoading();

  const loadRoleUsers = async (roleId?: number) => {
    const { data } = await request(`/org/sysRole/getUserByRole`, {
      params: {
        roleId: roleId || currentRole.value.id,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    roleUsers.value = data || [];
  };

  const handleRoleChange = async (role: any) => {
    setLoading(true);

    const { data: r } = await request(`/org/sysRole/${role.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    r.permissionCodes = r.permissionCodes || [];
    currentRole.value = r;

    perms.value = {
      teacher: teacherPermNodes,
      admin: adminPermNodes,
    };

    grantedPermissionSet.value = Object.values(permsFlat.value)
      .filter((node) => {
        return (
          r.permissionCodes.some(
            (key) => matcher.match(key, node.permission) || matcher.match(key, node.permission.replace(':*', '')),
          ) || r.permissionCodes.includes(node.permission)
        );
      })
      .map((node) => node.permission);

    Object.keys(perms.value).forEach((key) => {
      const groups = perms.value[key].filter((item: any) => item.children?.length > 0);
      groups.forEach((flNode: menuService.UserMenu) => {
        flNode.children?.forEach((slNode: menuService.UserMenu) => {
          let slAllChecked = false;
          if (slNode.children?.length) {
            slAllChecked = slNode.children.every((node) => grantedPermissionSet.value.includes(node.permission));
          } else {
            slAllChecked =
              grantedPermissionSet.value.includes(slNode.permission) ||
              grantedPermissionSet.value.some((code) => matcher.match(code, slNode.permission?.toString()));
          }
          if (slAllChecked) {
            grantedPermissionSet.value.push(slNode.permission);
          }
        });
      });
    });

    try {
      await loadRoleUsers(role.id);
    } finally {
      setLoading(false);
    }
  };

  const handleNodeChange = (node: any) => {
    currentNode.value = node;
  };

  const simplifyGrantedPermissions = (granted: string[]) => {
    const result: string[] = [];

    const getLeaves = (node: any) => {
      const leaves: string[] = [];
      if (node.children) {
        node.children.forEach((child: any) => {
          leaves.push(...getLeaves(child));
        });
      } else {
        leaves.push(node.permission!);
      }
      return leaves;
    };

    Object.keys(perms.value).forEach((key) => {
      const nodes = perms.value[key];
      nodes.forEach((node) => {
        const leaves = getLeaves(node);
        if (leaves.every((leaf) => granted.includes(leaf))) {
          result.push(node.permission);
        } else {
          node.children?.forEach((child) => {
            const subLeaves = getLeaves(child);
            if (subLeaves.every((leaf) => granted.includes(leaf))) {
              result.push(child.permission);
            } else {
              result.push(...subLeaves.filter((leaf) => granted.includes(leaf)));
            }
          });
        }
      });
    });

    // result need unique
    return Array.from(new Set(result));
  };

  const handleNodeCheck = (checkedKeys: (string | number)[], e: any): any => {
    const group = currentNode.value.permission.split(':')[0];
    const raw = (currentRole.value.permissionCodes || []).filter((code: string) => !code.startsWith(`${group}:`));

    const checked = [...raw, ...checkedKeys];
    const catalogIndexes = permsIndex.value[activeTab.value];

    grantedPermissionSet.value = Object.values(permsFlat.value)
      .filter((node) => {
        const prefix = node.permission.split(':')[0];
        return checked.some((key) => {
          return (
            matcher.match(key, node.permission) ||
            (checked.includes(node.permission) && catalogIndexes.includes(prefix))
          );
        });
      })
      .map((node) => node.permission);

    grantedPermissionSet.value.push(
      activeTab.value === 'teacher' ? 'teacherAbility:teacherClient' : 'adminAbility:managerClient',
    );

    currentRole.value.permissionCodes = simplifyGrantedPermissions(grantedPermissionSet.value);
  };

  /**
   * 全模块授权
   * 需要考虑半选状态下的处理
   * 1. 如果当前分组已有选中，但是非全选 则全选
   * 2. 如果当前分组中已全选，则取消全选
   * 3. 如果当前分组中无选中，则全选
   * @param rootNode
   * @param checked
   */
  const handleWholeModuleCheck = (rootNode: menuService.UserMenu, checked: boolean) => {
    currentRole.value.permissionCodes = currentRole.value.permissionCodes?.filter(
      (code: string) => !code.startsWith(`${rootNode.key}:`),
    );
    if (!checked) {
      grantedPermissionSet.value = grantedPermissionSet.value.filter(
        (code: string) => !code.startsWith(`${rootNode.key}:`),
      );
      return;
    }
    let permissionCodes = [...(currentRole.value.permissionCodes || [])];
    const { permission } = rootNode;
    const group = permission?.toString().split(':')[0];
    const groupChecked = groupsCheckStatus.value[group];
    if (groupChecked === 1) {
      Object.values(permsFlat.value)
        .filter((node) => node.permission.startsWith(`${group}:`))
        .forEach((node) => {
          permissionCodes = permissionCodes.filter((code: string) => code !== node.permission);
        });
    } else {
      Object.values(permsFlat.value)
        .filter((node) => node.permission.startsWith(`${group}:`))
        .forEach((node) => {
          if (!permissionCodes.includes(node.permission)) {
            permissionCodes.push(node.permission);
          }
        });
    }

    const catalogIndexes = permsIndex.value[activeTab.value];

    grantedPermissionSet.value = permissionCodes?.filter((item) => {
      const prefix = item.split(':')[0];
      return catalogIndexes.includes(prefix);
    });

    grantedPermissionSet.value.push(
      activeTab.value === 'teacher' ? 'teacherAbility:teacherClient' : 'adminAbility:managerClient',
    );

    currentRole.value.permissionCodes = [...(currentRole.value.permissionCodes || []), permission]?.filter((item) => {
      const prefix = item.split(':')[0];
      return catalogIndexes.includes(prefix);
    });
  };

  const handleSave = async () => {
    setLoading(true);
    const permissionCodes = currentRole.value.permissionCodes || [];

    try {
      await request(`/org/sysRole/${currentRole.value.id}`, {
        method: 'PUT',
        data: {
          ...currentRole.value,
          permissionCodes,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('保存授权成功');
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    setLoading(true);
    try {
      const { data } = await request('/org/sysRole', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      const roles = {};
      const others = [];
      const raw = (data.items || []).sort((a: any, b: any) => a.systemRole - b.systemRole);
      raw.forEach((role: any) => {
        if (role.unitNature) {
          const key = role.unitNature ? UNIT_NATURES_MAP[role.unitNature] : '其他';
          roles[key] = roles[key] || [];
          roles[key].push(role);
        } else {
          others.push(role);
        }
      });

      allRoles.value = [];

      Object.keys(roles).forEach((group) => {
        allRoles.value.push({
          group,
          roles: roles[group],
        });
      });

      allRoles.value.push({
        group: '其他',
        roles: others,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleShowEdit = (role: any) => {
    editData.value = role;
    editVisible.value = true;
  };

  const handleDeleteRole = async (role: any) => {
    setLoading(true);
    try {
      await request(`/org/sysRole/${role.id}`, {
        method: 'DELETE',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('删除岗位成功');
      if (role.id === currentRole.value.id) {
        currentRole.value = {};
        grantedPermissionSet.value = [];
        roleUsers.value = [];
      }
      await loadRoles();
    } finally {
      setLoading(false);
    }
  };

  onMounted(async () => {
    await loadRoles();
    // eslint-disable-next-line prefer-destructuring
    currentNode.value = tabs.value[0].nodes[0] || {};
  });
</script>

<template>
  <div class="wrapper flex gap-2">
    <a-card title="岗位授权">
      <template #extra>
        <a-button
          v-if="userStore.isAuthorized('system:org:authorize')"
          size="mini"
          class="ml-4"
          type="text"
          @click="() => handleShowEdit({})"
        >
          <template #icon>
            <IconPlus />
          </template>
          创建新岗位
        </a-button>
      </template>
      <a-spin :loading="loading">
        <a-collapse accordion :bordered="false">
          <a-collapse-item v-for="group in allRoles" :key="group.group" :header="group.group">
            <div
              v-for="role in group.roles"
              :key="role.id"
              class="cursor-pointer rounded-lg py-1 my-1 px-2 flex justify-between gap-2 role-item bg-"
              :class="{
                'bg-gray-200': currentRole.id === role.id,
                'hover:bg-gray-100': currentRole.id !== role.id,
              }"
            >
              <div class="flex-1 flex items-center gap-1" @click="() => handleRoleChange(role)">
                <a-tooltip v-if="role.systemRole" :content="`系统预置岗位`">
                  <IconInfoCircle />
                </a-tooltip>
                {{ role.name }}
              </div>
              <div class="actions group-hover:opacity-100">
                <a-button
                  v-if="!role.systemRole"
                  size="mini"
                  type="text"
                  class="text-indigo-600"
                  @click="() => handleShowEdit(role)"
                >
                  <template #icon>
                    <IconEdit />
                  </template>
                </a-button>
                <a-popconfirm
                  v-if="!role.systemRole"
                  type="error"
                  class="w-80"
                  :content="`确定要删除 ${role.name} 这个岗位吗？岗位下用户将不再拥有此岗位权限`"
                  @ok="() => handleDeleteRole(role)"
                >
                  <a-button size="mini" type="text" class="text-red-600">
                    <template #icon>
                      <IconDelete />
                    </template>
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
          </a-collapse-item>
        </a-collapse>
      </a-spin>
    </a-card>
    <a-card class="flex-1 tab-wrapper">
      <a-tabs v-if="currentRole.id" v-model:active-key="activeTab">
        <a-tab-pane v-for="tab in tabs" :key="tab.key" :title="tab.label">
          <div v-if="currentRole.description">
            <a-alert type="info" show-icon class="mb-2">
              {{ currentRole.description }}
              <div v-if="currentRole.systemRole" class="text-sm text-gray-500">
                系统预置岗位，无法修改，仅用于查看权限
              </div>
            </a-alert>
          </div>
          <div class="flex">
            <div>
              <div
                v-for="node in tab.nodes"
                :key="node.key"
                class="border-0 border-solid border-b p-2 border-b-slate-200 flex gap-2 items-center w-42"
                @click="() => handleNodeChange(node)"
              >
                <a-tooltip content="全模块授权">
                  <!--  disabled:                || currentRole.systemRole-->
                  <a-checkbox
                    size="small"
                    type="round"
                    :disabled="!node.children"
                    :model-value="groupsCheckStatus[node.key] === 1"
                    :indeterminate="groupsCheckStatus[node.key] === 2"
                    @change="(e: any) => handleWholeModuleCheck(node, e, tab.key)"
                  />
                </a-tooltip>
                <div
                  class="cursor-pointer flex-1 flex gap-2 justify-between items-center"
                  :class="{
                    'text-indigo-600 font-bold': currentNode.permission === node.permission,
                  }"
                  >{{ node.label }}
                  <IconDoubleRight v-if="currentNode.permission === node.permission" />
                </div>
              </div>
            </div>
            <div
              v-if="currentNode.permission"
              class="border-0 border-solid border-l border-l-slate-300 flex-1 ml-4 pl-4"
            >
              <a-tree
                v-if="currentNode.children"
                checked-strategy="parent"
                :data="currentNode.children"
                :checked-keys="grantedPermissionSet"
                :default-expanded-keys="[]"
                :selected-keys="[]"
                auto-expand-parent
                :checkable="true"
                multiple
                size="medium"
                :show-line="true"
                :field-names="{ title: 'label', key: 'permission', disableCheckbox: 'disableCheckbox' }"
                @check="handleNodeCheck"
              >
                <template #title="node">
                  <div class="flex gap-2 items-center">
                    <div>{{ node.label }}</div>
                    <a-tooltip v-if="node.desc" :content="node.desc">
                      <IconQuestionCircle />
                    </a-tooltip>
                  </div>
                </template>
              </a-tree>
            </div>
          </div>
        </a-tab-pane>
        <template #extra>
          <!--   v-if="!currentRole.systemRole" -->
          <a-button size="mini" type="primary" @click="handleSave">
            <template #icon>
              <IconSave />
            </template>
            保存
            {{ currentRole.name }}
            授权
          </a-button>
        </template>
      </a-tabs>
      <a-empty v-else-if="!loading" class="mt-20 mb-2" description="请先在左侧岗位列表中选择岗位" />
    </a-card>
    <a-card v-if="currentRole.id" :title="`${currentRole.name} 岗位的用户`">
      <div class="grid grid-cols-3 gap-2 text-center mb-2">
        <div class="flex flex-col items-center">
          <a-tooltip :content="`点击管理 [${currentRole.name}] 岗位的用户`">
            <avatar-display
              :size="40"
              :user-info="{ name: '管理', id: 'NEW' }"
              @click="() => (userManageVisible = true)"
            >
              <template #content>
                <IconEdit />
              </template>
            </avatar-display>
            <div class="py-1"> 管理</div>
          </a-tooltip>
        </div>
        <div v-for="user in roleUsers" :key="user.id" class="flex flex-col items-center">
          <avatar-display :size="40" :user-info="user" :user-card="true" />
          <div class="py-1">{{ user.name }}</div>
        </div>
      </div>
    </a-card>

    <role-edit v-model:visible="editVisible" v-model="editData" @ok="loadRoles" />
    <user-manage
      v-if="userManageVisible"
      v-model="userManageVisible"
      :role="currentRole"
      :role-users="roleUsers"
      @ok="loadRoleUsers"
    />
  </div>
</template>

<style scoped lang="less">
  .tab-wrapper {
    :deep(.arco-card-body) {
      padding-top: 4px;
    }
  }

  .role-item {
    .actions {
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover {
      .actions {
        opacity: 1;
      }
    }
  }

  :deep {
    .arco-collapse-item-content {
      padding: 0 8px;
    }
  }
</style>
