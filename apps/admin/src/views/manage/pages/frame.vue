<script setup lang="ts">
  import { PROJECT_URLS } from '@repo/env-config';
  import { onBeforeRouteUpdate, useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { computed, onMounted, ref } from 'vue';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = ref<any>();

  onMounted(() => {
    menuInfo.value = menuStore.getCurrentMenuInfo(route);
  });

  onBeforeRouteUpdate((to) => {
    menuInfo.value = menuStore.getCurrentMenuInfo(to);
  });

  const frameSrc = computed(() => {
    const modulePath = menuInfo.value?.subModules?.join('/');
    return `${PROJECT_URLS.MAIN_PROJECT}/centerManage.html#/${modulePath}`;
  });
</script>

<template>
  <iframe :src="frameSrc" class="frame" />
</template>

<style scoped lang="less">
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }
</style>
