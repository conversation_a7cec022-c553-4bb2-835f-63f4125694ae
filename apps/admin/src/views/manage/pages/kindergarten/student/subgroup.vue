<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import TeacherSelect from '@repo/components/org/teacherSelect.vue';
  import { computed, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { cloneDeep } from 'lodash';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getAdminClientNature } from '@repo/components/utils/utils';
  import useCommonStore from '@repo/infrastructure/utils/store';

  const schema = ref(null);

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const teacherListVisible = ref(false);
  const studentListVisible = ref(false);
  const currentRow = ref(null);
  const tableRef = ref(null);
  const { loading, setLoading } = useLoading();
  const teacherSelectRef = ref(null);

  const queryParams = {
    schoolNature: menuInfo.app.label,
    sort: '-id',
    orgNature: getAdminClientNature(),
  };
  const studentList = ref([]);
  const filterStudentList = ref([]);
  const targetStudent = ref([]);
  const originalTarget = ref([]);
  const deletedFromOriginalTarget = ref([]);
  const originalTeacher = ref([]);
  const deletedTeacher = ref([]);
  const selectedGradClass = ref();
  const selectedSchool = ref();
  const currentStudentName = ref();

  // const allStudentList = computed(() => {
  //   return studentList.value.map((student) => ({ label: student.name, value: student.id }));
  // });
  const allStudentList = computed(() => {
    return studentList.value
      .filter((s) => s.branchOfficeId === currentRow.value?.boId)
      .map((student) => ({ label: student.name, value: student.id }));
  });

  const handleRowAction = async (action: Record<string, any>, record: any) => {
    currentRow.value = cloneDeep(record);
    switch (action.key) {
      case 'teacherListMaintain':
        teacherListVisible.value = true;
        originalTeacher.value = currentRow.value?.teacherList.map((teacher) => teacher.id) || [];
        break;
      case 'studentListMaintain':
        filterStudentList.value = currentRow.value.studentList.map((student: any) => {
          return { value: student.id, label: student.name };
        });
        targetStudent.value = currentRow.value.studentList.map((item) => item.id);
        originalTarget.value = cloneDeep(targetStudent.value);
        studentListVisible.value = true;
        break;
      default:
        break;
    }
  };
  const resetData = () => {
    currentRow.value = null;
    selectedGradClass.value = null;
    currentStudentName.value = null;
    targetStudent.value = [];
  };
  const handleClose = () => {
    teacherListVisible.value = false;
    studentListVisible.value = false;
    currentRow.value = null;
    resetData();
  };
  const filterStudentIds = computed(() => {
    return filterStudentList.value.map((item) => item.value) || [];
  });

  const handleSearch = (val: any) => {
    const students = studentList.value
      .filter((student) => student.name === val && !filterStudentIds.value.includes(student.id))
      .map((item) => {
        return {
          label: item.name,
          value: item.id,
        };
      });
    filterStudentList.value = [...filterStudentList.value, ...students];
  };
  const handleSelectTeacher = (id, teacher: any) => {
    deletedTeacher.value = deletedTeacher.value.filter((item) => item !== teacher.id);
    const exists = currentRow.value.teacherList.find((item) => item.id === teacher.id);
    if (exists) {
      Message.error('该教师已存在');
      return;
    }
    currentRow.value.teacherList.push({
      id: teacher.id,
      name: teacher.name,
      fixed: false,
      courses: [],
      remark: '',
    });
  };

  const handleDeleteTeacher = async () => {
    try {
      await request(`/resourceRoom/subgroup/removeTeacherFromGroup/${currentRow.value.id}`, {
        method: 'delete',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: deletedTeacher.value,
      });
      Message.success('教师移除成功');
    } finally {
      /**/
    }
  };

  const handleSaveTeachersList = async () => {
    setLoading(true);
    try {
      if (deletedTeacher.value.length > 0) await handleDeleteTeacher();
      await request(`/resourceRoom/subgroup/${currentRow.value.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...currentRow.value,
          managerUserIds: currentRow.value.teacherList
            .filter((item) => {
              return item.fixed === true;
            })
            .map((item) => {
              return item.id;
            }),
        },
      });
      tableRef.value.handleLoadData();
      return true;
    } catch (e) {
      Message.error('修改分组教师失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const deleteTeacher = async (record: any) => {
    deletedTeacher.value.push(record.id);
    currentRow.value.teacherList = currentRow.value.teacherList.filter((item) => item.id !== record.id);
  };

  const boStore = useCommonStore({
    api: '/resourceRoom/student',
    queryParams: {
      ...queryParams,
    },
  });

  const studentsList = ref();
  const handleRemoveStudentFromGroup = async () => {
    await request(`/resourceRoom/subgroup/removeStudentFromGroup/${currentRow.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'delete',
      data: deletedFromOriginalTarget.value,
    });
  };

  const handleSaveStudentList = async () => {
    if (!studentsList.value) {
      studentListVisible.value = false;
      selectedGradClass.value = null;
      return;
    }
    try {
      await handleRemoveStudentFromGroup();
      setLoading(true);
      await request(`/resourceRoom/subgroup/${currentRow.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'put',
        data: {
          ...currentRow.value,
          studentList: studentsList.value.map((item) => {
            return {
              id: item.id,
              name: item.name,
            };
          }),
        },
      });
      tableRef.value.handleLoadData();
    } finally {
      resetData();
      setLoading(false);
    }
  };

  const school = computed(() => {
    const uniqueGradeClasses = new Map();
    studentList.value.forEach((item) => {
      if (item.fusionSchool && !uniqueGradeClasses.has(item.fusionSchool.id)) {
        uniqueGradeClasses.set(item.fusionSchool.id, {
          id: item.fusionSchool.id,
          name: item.fusionSchool.name,
        });
      }
    });
    return Array.from(uniqueGradeClasses.values());
  });

  const handleSchoolChange = (val: number) => {
    filterStudentList.value = [];
    selectedGradClass.value = null;
    studentList.value.forEach((item) => {
      if (item.fusionSchool?.id === val || targetStudent.value.includes(item.id)) {
        filterStudentList.value.push({ label: item.name, value: item.id });
      }
    });
  };

  const handleTransferChange = (val: any) => {
    studentsList.value = studentList.value.filter((item) => targetStudent.value.includes(item.id));
    const keep = originalTarget.value.filter((item) => targetStudent.value.includes(item)) || [];
    deletedFromOriginalTarget.value = originalTarget.value.filter((item) => !keep.includes(item));
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/subgroup');
    studentList.value = await boStore.getList();
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    module-name="分组"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['boId', 'name', 'teacherList', 'createdDate']"
    @row-action="handleRowAction"
  >
    <a-modal
      v-model:visible="teacherListVisible"
      :width="800"
      title="分组教师管理"
      :on-before-ok="handleSaveTeachersList"
      :ok-loading="loading"
      :render-to-body="false"
      @close="handleClose"
    >
      <teacher-select
        v-if="currentRow"
        ref="teacherSelectRef"
        :show-role="true"
        :default-query-params="{
          branchOffice: currentRow.grade?.school?.branchOfficeId,
          orgNature: currentRow?.orgNature,
        }"
        :multiple="true"
        @change="handleSelectTeacher"
      />

      <a-table :data="currentRow?.teacherList || []" class="mt-2" :pagination="false">
        <template #columns>
          <a-table-column title="教师" data-index="name"></a-table-column>
          <a-table-column title="负责人" data-index="fixed">
            <template #cell="{ record }">
              <a-switch v-model="record.fixed" size="small" type="round" />
            </template>
          </a-table-column>
          <a-table-column title="备注" data-index="remark">
            <template #cell="{ record }">
              <a-input v-model="record.remark" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-button type="text" size="mini" @click="() => deleteTeacher(record)"> 删除 </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
    <a-modal
      v-model:visible="studentListVisible"
      :width="650"
      title="学生维护"
      :on-before-ok="handleSaveStudentList"
      :render-to-body="false"
      @close="handleClose"
    >
      <a-form auto-label-width>
        <a-row v-if="false">
          <a-col :span="24">
            <a-form-item label="请选择学校">
              <div class="flex justify-around">
                <a-select
                  v-model="selectedSchool"
                  :options="school"
                  :field-names="{ label: 'name', value: 'id' }"
                  allow-search
                  size="mini"
                  :disabled="true"
                  @change="handleSchoolChange"
                />
                <a-input-search
                  v-model="currentStudentName"
                  size="mini"
                  class="ml-2"
                  placeholder="学生搜索"
                  @search="handleSearch"
                />
              </div>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="请选择学生">
          <a-transfer
            v-model="targetStudent"
            :source-input-search-props="{
              placeholder: '请输入学生姓名',
            }"
            :target-input-search-props="{
              placeholder: '请输入学生姓名',
            }"
            show-search
            :data="allStudentList"
            one-way
            @change="handleTransferChange"
          >
            <template #source-title="{ onSelectAllChange }">
              <div class="flex justify-start items-center">
                <a-checkbox class="mr-2" @change="onSelectAllChange" />
                <span>学生列表</span>
              </div>
            </template>
            <template #target-title="{ onClear }">
              <div class="flex justify-between items-center">
                <span>分组学生列表</span>
                <icon-delete @click="onClear" />
              </div>
            </template>
          </a-transfer>
        </a-form-item>
      </a-form>
    </a-modal>
  </table-with-modal-form>
</template>

<style scoped lang="scss">
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
</style>
