<script setup lang="ts">
  import StudentList from '@repo/components/student/studentList.vue';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import StudentIndex from '@/views/manage/common/student/studentIndex.vue';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const queryParams = {
    schoolNature: menuInfo.app.label,
  };

  const visibleColumns = [
    'name',
    'symbol',
    'nation',
    'fusionSchool',
    'gradeClass',
    'hasSendPlan',
    'status',
    'gender',
    'age',
    'birthday',
    'disorders',
  ];
</script>

<template>
  <student-list
    :title="menuInfo.app.label"
    :visible-columns="visibleColumns"
    :default-query-params="queryParams"
    module-path="/manage/specialSchool/student"
  />
</template>

<style scoped lang="scss"></style>
