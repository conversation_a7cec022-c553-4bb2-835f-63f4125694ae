<script setup lang="ts">
  import StudentList from '@repo/components/student/studentList.vue';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const queryParams = {
    schoolNature: menuInfo.app.label,
    _og: '1', // only graduated
    status: 'Graduation',
  };

  const visibleColumns = [
    'name',
    'symbol',
    'nation',
    'fusionSchool',
    'status',
    'graduatedSetTime',
    'gotoAfterGraduation',
    'gender',
    'age',
    'birthday',
    'disorders',
  ];

  const schema = ref<any>();

  onMounted(async () => {
    const studentSchema = await SchemaHelper.getInstanceByDs('/resourceRoom/student');

    schema.value = {
      ...studentSchema,
      importable: {
        enabled: false,
      },
      listViewProps: {
        searchType: 'quick',
      },
      rowActions: [...studentSchema.rowActions, { key: 'edit', visible: false }, { key: 'delete', visible: false }],
    };
  });
</script>

<template>
  <student-list
    v-if="schema"
    :custom-schema="schema"
    :visible-columns="visibleColumns"
    :default-query-params="queryParams"
    :table-action-props="{
      visibleComponents: ['refresh', 'quickSearch'],
    }"
    module-path="/manage/specialSchool/student/graduated"
  >
    <template #title> {{ menuInfo.app.label }} 毕业生信息库 </template>
  </student-list>
</template>

<style scoped lang="scss"></style>
