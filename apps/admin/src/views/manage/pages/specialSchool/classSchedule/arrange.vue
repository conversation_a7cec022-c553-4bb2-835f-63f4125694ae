<script setup lang="ts">
  import { computed, nextTick, onMounted, provide, ref, watch } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { useRoute, useRouter } from 'vue-router';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message, Modal } from '@arco-design/web-vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import GradeHours from '@/views/manage/components/classSchedule/gradeHours.vue';
  import ArrangeConditions from '@/views/manage/components/classSchedule/arrangeConditions.vue';
  import Generation from '@/views/manage/components/classSchedule/generation.vue';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import TeachingConfig from '@/views/manage/components/classSchedule/teachingConfig.vue';
  import teacherTeaching from '@/modules/org/schema/teacherTeaching';
  import { cloneDeep } from 'lodash';

  const activeTab = ref('teachingConfig');
  // const activeTab = ref('generation');
  const route = useRoute();
  const router = useRouter();
  const { loading, setLoading } = useLoading();
  const schoolCourseStore = useSchoolCourseStore();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const task = ref<any>(null);

  const grades = ref<any[]>([]);
  const gradesMap = ref<any>({});
  const allLessons = ref<any[]>([]);
  const allLessonsCopy = ref<any[]>([]);
  const allLessonsMap = ref<any>({});
  const gradeClasses = ref<any[]>([]);
  const gradeClassesMap = ref<any>({});
  const teacherConfigs = ref<any[]>([]);

  const loadingMessage = ref('');
  const systemLoading = ref(true);

  const teachersMap = computed(() => {
    const map = {};
    if (!teacherConfigs.value.length) {
      return map;
    }
    teacherConfigs.value.forEach((config) => {
      map[config.teacher.id] = config.teacher;
    });
    return map;
  });

  const getTask = async () => {
    const { data } = await request(`/teacher/lessonSchedule/${route.query.taskId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    task.value = data;
  };

  const getGrades = async () => {
    const { data } = await request('/teacher/schoolGrade', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
        graduate: false,
        orgNature: menuInfo.app?.label,
      },
    });

    const schoolIdsSet = new Set(data.items?.map((grade) => grade.school?.id));
    if (schoolIdsSet.size > 1) {
      Modal.error({
        title: '错误',
        content: '您账号下存在多个学校，无法进行排课',
        onOk: () => {
          router.back();
        },
      });
    }

    grades.value = data.items;
    gradesMap.value = data.items.reduce((acc, grade) => {
      acc[grade.id] = grade;
      return acc;
    }, {});
  };

  const getTeachers = async () => {
    const { data } = await request('/teacher/teacherTeachingConfig', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        lessonSchedule: task.value.id,
        pageSize: 999,
      },
    });

    teacherConfigs.value = data.items?.sort((a, b) => b.classHoursRequired - a.classHoursRequired) || [];
  };

  const getGradeClasses = async () => {
    const { data } = await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        'pageSize': 999,
        'orgNature': menuInfo.app?.label,
        'grade.graduate': false,
      },
    });

    gradeClasses.value = data.items || [];
    gradeClassesMap.value = data.items.reduce((acc, gradeClass) => {
      acc[gradeClass.id] = gradeClass;
      return acc;
    }, {});
  };

  const getLessons = async () => {
    allLessons.value = await schoolCourseStore.getSchoolCourses();
    allLessonsMap.value = await schoolCourseStore.getSchoolCoursesMap();
  };

  const handleSave = async () => {
    setLoading(true);
    Message.loading('保存中...');
    try {
      const { data } = await request(`/teacher/lessonSchedule/${task.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'PUT',
        data: task.value,
      });
      task.value = data;
      Message.clear();
      Message.success('保存成功');
    } finally {
      setLoading(false);
    }
  };

  const handleModifyConfig = async (item: any, type: string) => {
    let lessonIds = task.value?.noNeedScheduleLessonIds || [];
    let gradeClassIds = task.value?.noNeedScheduleClassIds || [];
    switch (type) {
      case 'add-lesson':
        lessonIds = lessonIds.filter((lessonId) => lessonId !== item.id);
        break;
      case 'del-lesson':
        lessonIds.push(item.id);
        break;
      case 'add-gradeClass':
        gradeClassIds = gradeClassIds.filter((gradeClassId) => gradeClassId !== item.id);
        break;
      case 'del-gradeClass':
        gradeClassIds.push(item.id);
        break;
      default:
        break;
    }
    task.value.noNeedScheduleLessonIds = lessonIds;
    task.value.noNeedScheduleClassIds = gradeClassIds;
    const { data } = await request(`/teacher/lessonSchedule/${task.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'PUT',
      data: task.value,
    });
    task.value = data;
    Message.success('保存成功');
  };

  const isInclude = (source: number[], compare: number): boolean => {
    return source?.includes(compare);
  };
  const filterNoNeedScheduledLesson = () => {
    if (task.value.noNeedScheduleLessonIds) {
      const noNeedIds = task.value.noNeedScheduleLessonIds || [];
      allLessonsCopy.value = allLessons.value.filter((lessonId) => !noNeedIds.includes(lessonId.id));
    } else {
      allLessonsCopy.value = cloneDeep(allLessons.value);
    }
  };
  onMounted(async () => {
    systemLoading.value = true;
    loadingMessage.value = '正在加载基础信息... 加载年级';
    await getGrades();
    loadingMessage.value = '正在加载基础信息... 加载班级';
    await getGradeClasses();
    loadingMessage.value = '正在加载基础信息... 加载课程';
    await getLessons();

    loadingMessage.value = '正在加载排课任务...';
    await getTask();
    loadingMessage.value = '正在加载教师教学配置...';
    await getTeachers();
    loadingMessage.value = '加载完成，现在可以开始排课';

    filterNoNeedScheduledLesson();
    await nextTick(() => {
      systemLoading.value = false;
    });
  });
  watch(
    () => task.value?.noNeedScheduleLessonIds,
    () => {
      filterNoNeedScheduledLesson();
    },
    {
      deep: true,
    },
  );

  provide('getTeachers', getTeachers);
</script>

<template>
  <div v-if="task">
    <a-card>
      <div class="flex justify-between gap-2">
        <div class="flex gap-2"> {{ task.period }} 排课任务</div>
      </div>
    </a-card>
    <a-card class="mt-2">
      <a-tabs v-model:active-key="activeTab">
        <a-tab-pane key="teachingConfig" title="1、教师设置">
          <teaching-config v-model:raw="teacherConfigs" :task="task" />
        </a-tab-pane>
        <a-tab-pane key="courseConfig" title="2、课程设置">
          <div class="w-full h-screen">
            <div
              v-for="item in allLessonsMap"
              :key="item.id"
              class="w-full h-24 flex justify-between items-center bg-gray-100 p-2 mb-2 rounded"
            >
              <div>
                <span class="ml-2">{{ item?.name }}</span>
                <a-tag :color="isInclude(task?.noNeedScheduleLessonIds, item.id) ? 'red' : 'green'" class="ml-2">
                  {{ isInclude(task?.noNeedScheduleLessonIds, item.id) ? '已移除排课' : '已加入排课' }}
                </a-tag>
              </div>
              <div id="optional" class="flex justify-start space-x-2 mr-10">
                <a-button
                  v-if="isInclude(task?.noNeedScheduleLessonIds, item.id)"
                  size="mini"
                  type="primary"
                  @click="handleModifyConfig(item, 'add-lesson')"
                >
                  加入排课
                </a-button>
                <a-button
                  v-else
                  size="mini"
                  type="primary"
                  status="danger"
                  @click="handleModifyConfig(item, 'del-lesson')"
                >
                  移除排课
                </a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="class" title="3、班级设置">
          <div class="w-full h-screen">
            <div
              v-for="item in gradeClasses"
              :key="item.id"
              class="w-full h-24 flex justify-between items-center bg-gray-100 p-2 mb-2 rounded"
            >
              <div>
                <span class="ml-2">{{ item?.name }}</span>
                <a-tag :color="isInclude(task?.noNeedScheduleClassIds, item.id) ? 'red' : 'green'" class="ml-2">
                  {{ isInclude(task?.noNeedScheduleClassIds, item.id) ? '已移除排课' : '已加入排课' }}
                </a-tag>
              </div>
              <div id="optional" class="flex justify-start space-x-2 mr-10">
                <a-button
                  v-if="isInclude(task?.noNeedScheduleClassIds, item.id)"
                  size="mini"
                  type="primary"
                  @click="handleModifyConfig(item, 'add-gradeClass')"
                >
                  加入排课
                </a-button>
                <a-button
                  v-else
                  size="mini"
                  type="primary"
                  status="danger"
                  @click="handleModifyConfig(item, 'del-gradeClass')"
                >
                  移除排课
                </a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="classHour" title="4、课时计划">
          <grade-hours
            v-if="activeTab === 'classHour'"
            v-model:task="task"
            :lessons="allLessonsCopy"
            :grades="grades"
            :teacher-configs="teacherConfigs"
            :grade-classes="gradeClasses"
            :grade-classes-map="gradeClassesMap"
            @save="handleSave"
          />
        </a-tab-pane>
        <a-tab-pane key="condition" title="5、条件设定">
          <arrange-conditions
            v-if="activeTab === 'condition'"
            v-model:task="task"
            :grades="grades"
            :lessons="allLessonsCopy"
            @save="handleSave"
          />
        </a-tab-pane>
        <a-tab-pane key="generation" title="6、课表生成">
          <generation
            v-if="activeTab === 'generation'"
            v-model:task="task"
            :lessons="allLessonsCopy"
            :grades="grades"
            :teachers-map="teachersMap"
            :lessons-map="allLessonsMap"
            :teaching-configs="teacherConfigs"
            @save="handleSave"
          />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
  <a-modal
    v-model:visible="systemLoading"
    :closable="false"
    title="加载中"
    :esc-to-close="false"
    :mask-closable="false"
  >
    <IconLoading />
    排课系统加载中，请稍后. {{ loadingMessage }}
    <template #footer>
      <div></div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
