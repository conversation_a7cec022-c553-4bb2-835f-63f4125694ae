<template>
  <div class="courses-container">
    <a-card class="mb-4">
      <template #title>课程管理</template>
      <a-space direction="vertical" size="large" fill>
        <a-row justify="space-between">
          <a-col>
            <a-space>
              <a-button size="mini" type="primary" @click="showAddModal = true">
                <template #icon><icon-plus /></template>
                添加课程
              </a-button>
              <a-button size="mini" type="outline" @click="showClassroomModal = true"> 教室设置 </a-button>
              <a-input-search v-model="searchKeyword" placeholder="请输入课程名称搜索" search-button size="mini" />
            </a-space>
          </a-col>
        </a-row>

        <a-table
          :columns="columns"
          :data="filteredCourses"
          :pagination="{ pageSize: 10 }"
          row-key="id"
          :bordered="{ cell: true }"
        >
          <template #classroomName="{ record }">
            <span
              :class="record.classroomName ? '' : 'text-red-500 font-bold cursor-pointer'"
              @click="editCourseClassRoom(record)"
            >
              {{ record.classroomName || '未设置' }}
            </span>
          </template>
          <template #operations="{ record }">
            <a-space>
              <a-popconfirm content="确定要删除该课程吗？" type="warning" @ok="handleDelete(record)">
                <a-button type="text" status="danger" size="mini"> 删除 </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </a-space>
    </a-card>
    <a-modal
      v-model:visible="editCourseClassRoomVisible"
      :title="currentEdit?.name + ' - 教室设置'"
      :on-before-ok="handleEditPreOk"
    >
      <a-select v-model="formData.classroomId" placeholder="请选择教室" allow-clear @change="handleClassroomChange">
        <a-option v-for="classroom in classrooms" :key="classroom.id" :value="classroom.id">
          {{ classroom.name }}
        </a-option>
      </a-select>
    </a-modal>

    <a-modal
      v-model:visible="showAddModal"
      :title="editingCourse ? '编辑课程' : '添加课程'"
      @ok="handleSubmit"
      @cancel="resetForm"
    >
      <a-form ref="formRef" :model="formData">
        <a-form-item field="name" label="课程名称" required>
          <a-select
            v-model="selectedCourseList"
            multiple
            size="mini"
            placeholder="请选择课程"
            :options="courseOptions || []"
          />
        </a-form-item>
        <a-form-item v-if="formData.classroomId" field="capacity" label="容量限制">
          <a-input v-model="formData.capacity" readonly />
          <div class="text-xs text-gray-500 mt-1"> 该教室最多可容纳 {{ formData.capacity }} 个班级 </div>
        </a-form-item>
      </a-form>
      <div class="flex flex-wrap gap-2">
        <a-tag
          v-for="item in courses"
          :key="item.id"
          class="cursor-pointer"
          :color="randomColor(44)"
          closable
          @close="handleClose(item)"
        >
          {{ item.name }}
        </a-tag>
      </div>
    </a-modal>

    <a-modal v-model:visible="showClassroomModal" title="教室设置" width="800px" @cancel="() => {}">
      <div class="classroom-modal-content">
        <div class="add-classroom-form mb-4">
          <a-input-group class="flex space-x-2">
            <a-input v-model="newClassroom.name" placeholder="请输入教室名称" style="width: 200px" />
            <div>
              <a-input-number v-model="newClassroom.capacity" placeholder="容量限制" style="width: 150px" :min="1" />
              <a-button size="mini" type="primary" @click="handleAddClassroom"> 添加 </a-button>
            </div>
          </a-input-group>
          <div class="text-xs text-gray-500 mt-2"> 容量限制表示该教室最多可容纳的班级数量 </div>
        </div>

        <a-table :columns="classroomColumns" :data="classrooms" :pagination="false" row-key="id">
          <template #operations="{ record }">
            <a-space>
              <a-button type="text" size="mini" @click="handleEditClassroom(record)"> 编辑 </a-button>
              <a-popconfirm content="确定要删除该教室吗？" type="warning" @ok="handleDeleteClassroom(record)">
                <a-button type="text" status="danger" size="mini"> 删除 </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table>
      </div>
      <template #footer>
        <a-button size="mini" @click="showClassroomModal = false">关闭</a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="showEditClassroomModal"
      title="编辑教室"
      @ok="handleUpdateClassroom"
      @cancel="resetClassroomForm"
    >
      <a-form ref="classroomFormRef" :model="editingClassroomData">
        <a-form-item field="name" label="教室名称" required>
          <a-input v-model="editingClassroomData.name" placeholder="请输入教室名称" />
        </a-form-item>
        <a-form-item field="capacity" label="容量限制" required>
          <a-input-number v-model="editingClassroomData.capacity" :min="1" />
          <div class="text-xs text-gray-500 mt-1"> 容量限制表示该教室最多可容纳的班级数量 </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconPlus } from '@arco-design/web-vue/es/icon';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import { randomColor } from '@repo/ui/components/utils/randomColor';

  const props = defineProps({
    setting: {
      type: Object,
    },
    record: {
      type: Object,
    },
  });

  const emits = defineEmits(['update:setting', 'autoSave']);
  const generalSettings = computed({
    get: () => {
      return props.setting || {};
    },
    set: (val) => {
      emits('update:setting', val);
    },
  });

  const columns = [
    { title: '课程名称', dataIndex: 'name' },
    {
      title: '所属教室',
      dataIndex: 'classroomName',

      slotName: 'classroomName',
    },
    {
      title: '容量限制',
      dataIndex: 'capacity',
      render: ({ record }) => {
        return record.capacity || '-';
      },
    },
    { title: '操作', slotName: 'operations' },
  ];

  const classroomColumns = [
    { title: '教室名称', dataIndex: 'name' },
    { title: '容量限制', dataIndex: 'capacity' },
    { title: '操作', slotName: 'operations' },
  ];

  const courses = ref(props.setting?.courses);
  const classrooms = ref(props.setting?.classrooms || []);

  const searchKeyword = ref('');
  const showAddModal = ref(false);
  const showClassroomModal = ref(false);
  const showEditClassroomModal = ref(false);
  const editingCourse = ref(null);
  const editingClassroom = ref(null);
  const formRef = ref(null);
  const classroomFormRef = ref(null);
  const formData = ref({
    name: '',
    classroomId: null,
    capacity: null,
    classroomName: '',
  });

  const newClassroom = ref({
    name: '',
    capacity: 1,
  });

  const editingClassroomData = ref({
    name: '',
    capacity: 1,
  });
  const resetForm = () => {
    formData.value = {
      name: '',
      classroomId: null,
      capacity: null,
      classroomName: '',
    };
    editingCourse.value = null;
    showAddModal.value = false;
  };

  const resetClassroomForm = () => {
    editingClassroomData.value = {
      name: '',
      capacity: 1,
    };
    editingClassroom.value = null;
    showEditClassroomModal.value = false;
  };

  const filteredCourses = computed(() => {
    // .filter((course) => course?.name?.includes(searchKeyword.value)); // 搜索

    return courses.value.map((course) => {
      // 找到对应的教室信息
      const classroom = course.classroomId ? classrooms.value.find((c) => c.id === course.classroomId) : null;

      return {
        ...course,
        // 确保明确设置classroomId为null（如果不存在）
        classroomId: course.classroomId || null,
        classroomName: classroom ? classroom.name : '',
        capacity: classroom ? classroom.capacity : null,
      };
    });
  });

  const editCourseClassRoomVisible = ref(false);
  const currentEdit = ref();
  const editCourseClassRoom = (record: any) => {
    currentEdit.value = record;
    editCourseClassRoomVisible.value = true;
  };
  const handleEditPreOk = async () => {
    courses.value.forEach((item: any) => {
      if (item.id === currentEdit.value.id) {
        item.classroomId = formData.value.classroomId;
        item.classroomName = formData.value.classroomName;
      }
    });
    generalSettings.value.courses = courses;
  };
  const handleDelete = (course) => {
    courses.value = courses.value.filter((c) => c.id !== course.id);

    // 触发课程删除事件
    window.dispatchEvent(
      new CustomEvent('courseDeleted', {
        detail: { courseName: course.name },
      }),
    );

    Message.success('删除成功');
    generalSettings.value.courses = courses;
  };
  const courseOptions = ref([]);
  const selectedCourseList = ref(courses.value.map((item) => item?.id));

  const handleClose = (item: any) => {
    const res = courses.value.filter((c) => c.id !== item.id);
    courses.value = res;
    generalSettings.value.courses = courses;
  };
  const handleSubmit = () => {
    const isDuplicate = courses.value.some(
      (course) => course.name === formData.value.name && (!editingCourse.value || course.id !== editingCourse.value.id),
    );

    if (isDuplicate) {
      Message.warning('课程名称已存在，请使用其他名称');
      return;
    }

    if (editingCourse.value) {
      const oldName = editingCourse.value.name;
      const newName = formData.value.name;

      // 更新课程
      const index = courses.value.findIndex((c) => c.id === editingCourse.value.id);
      courses.value[index] = { ...editingCourse.value, ...formData.value };

      // 触发课程更新事件
      if (oldName !== newName) {
        window.dispatchEvent(
          new CustomEvent('courseUpdated', {
            detail: { oldName, newName },
          }),
        );
      }

      Message.success('编辑成功');
    } else {
      // selectedCourseList
      const exitsIds = courses.value.map((course) => course.id) || [];
      const addList = selectedCourseList.value.filter((c) => !exitsIds.includes(c));
      const addCourses = courseOptions.value
        .filter((c) => addList.includes(c.value))
        .map((c) => ({ id: c.value, name: c.label }));
      courses.value = [...courses.value, ...addCourses];

      Message.success('添加成功');
    }
    resetForm();
    generalSettings.value.courses = courses;
  };

  // 教室相关函数
  const handleAddClassroom = () => {
    if (!newClassroom.value.name.trim()) {
      Message.warning('请输入教室名称');
      return;
    }

    // 检查教室名称是否已存在
    const isDuplicate = classrooms.value.some((classroom) => classroom.name === newClassroom.value.name);

    if (isDuplicate) {
      Message.warning('教室名称已存在，请使用其他名称');
      return;
    }

    const newClassrooms = [
      ...classrooms.value,
      {
        id: Date.now(),
        name: newClassroom.value.name,
        capacity: newClassroom.value.capacity || 1,
      },
    ];

    classrooms.value = newClassrooms;
    generalSettings.value.classrooms = newClassrooms;

    // 重置输入
    newClassroom.value = {
      name: '',
      capacity: 1,
    };

    Message.success('添加成功');
  };

  const handleEditClassroom = (classroom) => {
    editingClassroom.value = classroom;
    editingClassroomData.value = { ...classroom };
    showEditClassroomModal.value = true;
  };

  const handleUpdateClassroom = () => {
    if (!editingClassroomData.value.name.trim()) {
      Message.warning('请输入教室名称');
      return;
    }

    // 检查教室名称是否已存在（排除当前编辑的教室）
    const isDuplicate = classrooms.value.some(
      (classroom) => classroom.name === editingClassroomData.value.name && classroom.id !== editingClassroom.value.id,
    );

    if (isDuplicate) {
      Message.warning('教室名称已存在，请使用其他名称');
      return;
    }

    // 更新教室数据
    const index = classrooms.value.findIndex((c) => c.id === editingClassroom.value.id);
    classrooms.value[index] = { ...editingClassroomData.value };

    // 更新关联的课程容量信息
    courses.value.forEach((course) => {
      if (course.classroomId === editingClassroom.value.id) {
        course.capacity = editingClassroomData.value.capacity;
      }
    });
    // 修改教室后关联更新数据以便保存
    generalSettings.value.classrooms = classrooms.value;
    generalSettings.value.courses = courses.value;

    Message.success('更新成功');
    resetClassroomForm();
  };

  const handleDeleteClassroom = (classroom) => {
    // 检查是否有课程关联了该教室
    const linkedCourses = courses.value.filter((course) => course.classroomId === classroom.id);

    if (linkedCourses.length > 0) {
      Message.warning(`该教室已关联 ${linkedCourses.length} 个课程，请先解除关联`);
      return;
    }

    classrooms.value = classrooms.value.filter((c) => c.id !== classroom.id);
    Message.success('删除成功');
  };

  const handleClassroomChange = (classroomId) => {
    if (!classroomId) {
      formData.value.capacity = null;
      return;
    }

    const selectedClassroom = classrooms.value.find((c) => c.id === classroomId);
    if (selectedClassroom) {
      formData.value.capacity = selectedClassroom.capacity;
    }
  };
  const loadCourse = async () => {
    const { data: res } = await request(`/teacher/schoolCourse/findByBoId/${props.record?.boId}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'get',
    });
    courseOptions.value = res.items.map((item) => ({ label: item?.name, value: item.id, row: item }));
  };
  onMounted(async () => {
    await loadCourse();
  });
  watch(
    () => props.setting,
    (newVal) => {
      emits('autoSave');
    },
    { deep: true },
  );
</script>
