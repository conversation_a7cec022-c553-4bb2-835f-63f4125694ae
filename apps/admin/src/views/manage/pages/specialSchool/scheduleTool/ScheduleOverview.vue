<template>
  <div class="schedule-overview p-4 min-h-screen bg-gray-50 rounded-lg">
    <!-- 主标签页系统 -->
    <a-tabs v-model:active-key="activeTabKey" :bordered="false" type="text">
      <!-- 添加 #extra 插槽用于放置右上角按钮 -->
      <template #extra>
        <a-space>
          <!-- 开始排课按钮（始终显示） -->
          <a-button
            type="primary"
            size="mini"
            :loading="isScheduling"
            :disabled="isOverCapacity"
            @click="startScheduling"
          >
            <template #icon><icon-play-arrow /></template>
            开始排课
          </a-button>

          <!-- 班级课表导入按钮 (仅在班级标签页激活时显示) -->
          <a-upload
            v-if="activeTabKey === 'class'"
            v-show="false"
            :show-upload-list="false"
            accept=".json"
            :custom-request="handleClassScheduleFileUpload"
          >
            <template #upload-button>
              <a-button type="outline" size="mini">
                <!-- 改为 outline 样式 -->
                <template #icon><icon-upload /></template>
                导入班级课表
              </a-button>
            </template>
          </a-upload>
          <!-- 教师课表导入按钮 (仅在教师标签页激活时显示) -->
          <a-upload
            v-if="activeTabKey === 'teacher'"
            :show-upload-list="false"
            accept=".json"
            :custom-request="handleTeacherScheduleFileUpload"
          >
            <template #upload-button>
              <a-button type="outline" size="mini">
                <template #icon><icon-upload /></template>
                导入教师课表
              </a-button>
            </template>
          </a-upload>
        </a-space>
      </template>

      <!-- 按班级查看标签页 -->
      <a-tab-pane key="class" title="按班级查看">
        <!-- 约束信息概览 -->
        <div class="mt-4 mb-6">
          <div class="flex items-center mb-6">
            <span class="text-lg font-medium text-gray-800">约束信息概览</span>
          </div>
          <a-row :gutter="[16, 16]">
            <!-- 课表容量检查卡片 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="6">
              <a-card class="metric-card" :bordered="false">
                <div class="flex items-center">
                  <div class="metric-icon bg-blue-100 text-blue-600 p-3 rounded-lg mr-4">
                    <IconCalendarClock />
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">总可用节次</div>
                    <div class="text-2xl font-bold">{{ totalAvailablePeriods }}</div>
                    <div class="text-xs text-gray-500 flex items-center mt-1">
                      总安排课时:
                      <span :class="{ 'text-red-500': isOverCapacity }" class="ml-1">{{ totalWeeklyHours }}</span>
                      <a-tag v-if="isOverCapacity" class="warning-tag ml-1" size="small" status="danger">
                        <span class="text-xs">超出{{ overCapacityCount }}</span>
                      </a-tag>
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            <!-- 特定教室课程卡片 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="6">
              <a-card class="metric-card" :bordered="false" @click="openCourseModal">
                <div class="flex items-center">
                  <div class="metric-icon bg-green-100 text-green-600 p-3 rounded-lg mr-4">
                    <IconApps />
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">特定教室课程</div>
                    <div class="text-2xl font-bold">{{ specialClassroomCourses.length }}</div>
                    <div class="text-xs text-blue-500 flex items-center mt-1">
                      查看详情 <IconRight class="ml-1" />
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            <!-- 教师可用时间卡片 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="6">
              <a-card class="metric-card" :bordered="false" @click="openTeacherModal">
                <div class="flex items-center">
                  <div class="metric-icon bg-purple-100 text-purple-600 p-3 rounded-lg mr-4">
                    <IconUser />
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">有特殊时间教师</div>
                    <div class="text-2xl font-bold">{{ teacherAvailability.length }}</div>
                    <div class="text-xs text-blue-500 flex items-center mt-1">
                      查看详情 <IconRight class="ml-1" />
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
            <!-- 连续授课限制卡片 -->
            <a-col :xs="24" :sm="24" :md="12" :lg="6">
              <a-card class="metric-card" :bordered="false" @click="openLimitModal">
                <div class="flex items-center">
                  <div class="metric-icon bg-orange-100 text-orange-600 p-3 rounded-lg mr-4">
                    <IconFilter />
                  </div>
                  <div>
                    <div class="text-sm text-gray-500">连续授课限制</div>
                    <div class="text-2xl font-bold">{{ consecutiveSettings.maxPeriods }}</div>
                    <div class="text-xs text-gray-500 flex items-center mt-1">
                      {{ consecutiveSettings.excludedTeachers.length }}名教师不受限
                      <IconRight class="ml-1 text-blue-500" />
                    </div>
                  </div>
                </div>
              </a-card>
            </a-col>
          </a-row>
        </div>
        <!-- 班级课表视图 -->
        <ClassScheduleView
          :class-schedule-data="classScheduleData"
          :record="record"
          @update-record="handleUpdateRecord"
        />
      </a-tab-pane>

      <!-- 按教师查看标签页 -->
      <a-tab-pane key="teacher" title="按教师查看">
        <!-- 注意：教师课表的手动上传功能已移至 TeacherScheduleView.vue 内部处理 -->
        <TeacherScheduleView :teacher-schedule-data="teacherScheduleData" :record="record" />
      </a-tab-pane>
    </a-tabs>

    <!-- <ScheduleView-->
    <!-- v-if="isReady"-->
    <!-- :record="record"-->
    <!-- :assignments="assignments"-->
    <!-- :view-type="activeTabKey"-->
    <!-- :grade-class-map="gradeClassMap"-->
    <!-- :grade-map="gradeMap"-->
    <!-- />-->
    <!-- 添加详情模态框 -->
    <a-modal
      v-model:visible="courseModalVisible"
      :width="700"
      title="特定教室课程详情"
      @cancel="courseModalVisible = false"
    >
      <a-empty v-if="!specialClassroomCourses.length" description="无需要特定教室的课程" />
      <div v-else class="detail-list">
        <div v-for="item in specialClassroomCourses" :key="item.courseId" class="detail-item">
          <div class="detail-content">
            <span class="detail-name">{{ item.courseName }}</span>
            <div class="detail-info">
              <a-tag size="small" class="classroom-tag">{{ item.classroomName }}</a-tag>
              <span v-if="item.capacity" class="capacity-text">({{ item.capacity }}班)</span>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <a-button size="mini" @click="courseModalVisible = false">关闭</a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="teacherModalVisible"
      :width="700"
      title="教师可用时间详情"
      @cancel="teacherModalVisible = false"
    >
      <a-empty v-if="!teacherAvailability.length" description="所有教师默认周一至周五可用" />
      <div v-else class="detail-list">
        <div v-for="item in teacherAvailability" :key="item.teacherId" class="detail-item">
          <!-- 修改这里，直接显示availabilityText -->
          <div class="detail-content">
            <span class="detail-name">{{ item.teacherName }}</span>
            <span class="detail-schedule">{{ item.availabilityText }}</span>
          </div>
        </div>
      </div>
      <template #footer>
        <a-button size="mini" @click="teacherModalVisible = false">关闭</a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:visible="limitModalVisible"
      :width="700"
      title="连续授课限制详情"
      @cancel="limitModalVisible = false"
    >
      <div class="detail-list">
        <div class="detail-section">
          <div class="detail-header">全局设置</div>
          <div class="detail-info-row">
            <span class="detail-label">连续授课上限：</span>
            <a-tag color="purple">{{ consecutiveSettings.maxPeriods }} 节</a-tag>
          </div>
        </div>

        <div class="detail-section mt-4">
          <div class="detail-header">不受连排上限限制的教师</div>
          <div class="excluded-list mt-2">
            <template v-if="consecutiveSettings.excludedTeachers.length > 0">
              <a-tag
                v-for="teacher in consecutiveSettings.excludedTeachers"
                :key="teacher.id"
                color="orange"
                class="teacher-tag"
              >
                {{ teacher.name }}
              </a-tag>
            </template>
            <span v-else class="no-teachers">无不受连排上限限制的教师</span>
          </div>
        </div>
      </div>
      <template #footer>
        <a-button size="mini" @click="limitModalVisible = false">关闭</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, onUnmounted, ref } from 'vue';
  import {
    IconApps,
    IconCalendarClock,
    IconFilter,
    IconPlayArrow,
    IconRight,
    IconUpload,
    IconUser,
  } from '@arco-design/web-vue/es/icon';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { request } from '@repo/infrastructure/request';
  import ClassScheduleView from '../../../components/scheduleTool/ClassScheduleView.vue';
  import TeacherScheduleView from '../../../components/scheduleTool/TeacherScheduleView.vue';
  import convertSettingToRequestParams from '../../../components/scheduleTool/utils/convertSettingToRequestParam.ts';

  const props = defineProps({
    setting: {
      type: Object,
    },
    record: {
      type: Object,
    },
  });
  const emits = defineEmits(['flushRecord']);
  // 状态管理
  const activeTabKey = ref('class');
  const record = ref(props.record);
  const handleUpdateRecord = (val) => {
    record.value = val;
    emits('flushRecord');
  };
  // 基础数据
  const grades = ref([]);
  const classes = ref([]);
  const courses = ref([]);
  const teachers = ref([]);
  const classrooms = ref([]);
  const consecutiveSettingsData = ref({ maxPeriods: 3, excludedTeachers: [] });
  const classScheduleData = ref({});
  const teacherScheduleData = ref({});

  // 课表容量相关数据
  const totalWeeklyHours = ref<any>(0);
  const totalAvailablePeriods = ref(0);

  // 计算属性
  const isOverCapacity = computed(() => totalWeeklyHours.value > totalAvailablePeriods.value);
  const overCapacityCount = computed(() =>
    isOverCapacity.value ? totalWeeklyHours.value - totalAvailablePeriods.value : 0,
  );

  // 事件处理函数
  const handleWeeklyScheduleUpdate = (event) => {
    if (event.detail?.totalWeeklyHours !== undefined) {
      totalWeeklyHours.value = event.detail.totalWeeklyHours;
    }
  };

  const handleConstraintsUpdate = (event) => {
    if (event.detail?.totalAvailablePeriods !== undefined) {
      totalAvailablePeriods.value = event.detail.totalAvailablePeriods;
    }
  };

  // 文件上传处理
  const handleClassScheduleFileUpload = (options) => {
    const { fileItem, onSuccess, onError } = options;
    const { file } = fileItem;

    if (!file || file.type !== 'application/json') {
      Message.error(file ? '请选择有效的 JSON 文件 (.json)' : '未选择文件');
      onError(file ? 'Invalid file type' : 'No file selected');
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target.result;
        const parsedData = JSON.parse(content);
        if (typeof parsedData !== 'object' || !parsedData) {
          throw new Error('JSON 文件内容不是有效的班级课表对象结构');
        }
        classScheduleData.value = parsedData;
        localStorage.setItem('class_schedule_raw', content);
        Message.success('班级课表数据导入成功！');
        onSuccess();
      } catch (error) {
        Message.error(`导入失败: ${error.message || '无法解析文件内容'}`);
        onError(error);
      }
    };
    reader.onerror = () => {
      Message.error('读取文件时发生错误');
      onError(new Error('File read error'));
    };
    reader.readAsText(file);
  };

  const handleTeacherScheduleFileUpload = (options) => {
    const { fileItem, onSuccess, onError } = options;
    const { file } = fileItem;

    if (!file || file.type !== 'application/json') {
      Message.error(file ? '请选择有效的 JSON 文件 (.json)' : '未选择文件');
      onError(file ? 'Invalid file type' : 'No file selected');
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target.result;
        const parsedData = JSON.parse(content);
        if (typeof parsedData !== 'object' || !parsedData) {
          throw new Error('JSON 文件内容不是有效的教师课表对象结构');
        }
        teacherScheduleData.value = parsedData;
        localStorage.setItem('teacher_schedule_raw', content);
        Message.success('教师课表数据导入成功！');
        onSuccess();
      } catch (error) {
        Message.error(`导入失败: ${error.message || '无法解析文件内容'}`);
        onError(error);
      }
    };
    reader.onerror = () => {
      Message.error('读取文件时发生错误');
      onError(new Error('File read error'));
    };
    reader.readAsText(file);
  };

  // 数据加载
  const loadData = () => {
    grades.value = props.setting?.grades || [];
    classes.value = props.setting?.classes || [];
    courses.value = props.setting?.courses || [];
    teachers.value = props.setting?.teachers || [];
    classrooms.value = props.setting?.classrooms || [];

    // 加载连续授课设置
    const consecutiveSettings = props.setting?.consecutiveTeachingSettings || {
      consecutiveLimit: 3,
      excludedTeacherIds: [],
    };

    consecutiveSettingsData.value = {
      maxPeriods: consecutiveSettings.consecutiveLimit || 3,
      excludedTeachers: consecutiveSettings.excludedTeacherIds || [],
    };

    // 加载统计数据
    const constraintsStats = props.setting?.constraintsStats || { totalAvailablePeriods: 0 };
    totalAvailablePeriods.value = constraintsStats.totalAvailablePeriods || 0;

    // 加载周课时
    const weeklySchedule = props.setting?.weeklySchedule || {};
    //
    const weeklyScheduleData = props.setting.weeklyScheduleData || {};
    let hours = 0;
    Object.entries(weeklyScheduleData).forEach(([k, v]: [string, any]) => {
      Object.entries(v?.teacherWeeklyHours).forEach(([key, val]: [string, any]) => {
        hours += Number(val) || 0;
      });
    });
    totalWeeklyHours.value = hours; /* Object.values(weeklySchedule).reduce(
      (total, item: any) => total + (item.weeklyRequiredHours || 0),
      0,
    ); */
  };

  // 辅助函数
  const getCourseName = (id) => courses.value.find((c) => c.id === id)?.name || '未知课程';
  const getTeacherName = (id) => teachers.value.find((t) => t.id === id)?.name || '未知教师';
  const getClassroomName = (id) => classrooms.value.find((c) => c.id === id)?.name || '未知教室';
  const formatWeekday = (day) => ['周一', '周二', '周三', '周四', '周五'][day] || '未知';

  // 约束相关计算属性
  const specialClassroomCourses = computed(() => {
    return courses.value
      .filter((course) => course.classroomId)
      .map((course) => ({
        courseId: course.id,
        courseName: getCourseName(course.id),
        classroomId: course.classroomId,
        classroomName: getClassroomName(course.classroomId),
        capacity: classrooms.value.find((c) => c.id === course.classroomId)?.capacity,
      }));
  });

  const teacherAvailability = computed(() => {
    return teachers.value
      .filter(
        (teacher) =>
          Array.isArray(teacher.available_weekdays) &&
          teacher.available_weekdays.length > 0 &&
          teacher.available_weekdays.length < 5,
      )
      .map((teacher) => ({
        teacherId: teacher.id,
        teacherName: getTeacherName(teacher.id),
        availabilityText: `仅 ${teacher.available_weekdays
          .sort((a, b) => a - b)
          .map(formatWeekday)
          .join(', ')} 可排课`,
      }));
  });
  const consecutiveSettings = computed(() => ({
    maxPeriods: consecutiveSettingsData.value.maxPeriods,
    excludedTeachers: (consecutiveSettingsData.value.excludedTeachers || []).map((id) => ({
      id,
      name: getTeacherName(id),
    })),
  }));

  // 模态框控制
  const courseModalVisible = ref(false);
  const teacherModalVisible = ref(false);
  const limitModalVisible = ref(false);

  const openCourseModal = () => {
    courseModalVisible.value = true;
  };
  const openTeacherModal = () => {
    teacherModalVisible.value = true;
  };
  const openLimitModal = () => {
    limitModalVisible.value = true;
  };

  // 排课相关
  const isScheduling = ref(false);

  // 开始排课功能
  const startScheduling = async () => {
    isScheduling.value = true;
    Message.loading('正在启动排课程序...');

    try {
      // 转换后的请求对象
      const requestBody = convertSettingToRequestParams(props.setting);
      // 发送请求等待排课
      const { data: res } = await request(`/teacher/scheduleTool/startSchedule/${props.record.boId}`, {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: requestBody || {},
        params: {
          taskId: props.record.id,
        },
      });
      record.value = res;
      emits('flushRecord', res);
      Message.success('排课程序已启动，请稍候...');
    } catch (error) {
      Message.error('排课启动失败，请重试');
    } finally {
      isScheduling.value = false;
    }
  };

  const loadTimeTable = async () => {
    const { data: res } = await request('/teacher/timetable/findTimetableByTaskId', {
      method: 'get',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: { schoolId: props.record.boId, taskId: props.record.id },
    });
  };
  const gradeMap = ref({});
  const gradeClassMap = ref({});

  const loadGradeClass = async () => {
    try {
      const { data: res } = await request('/resourceRoom/gradeClass', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'get',
        params: {
          boId: props.record.boId,
        },
      });
      res.items.forEach((item) => {
        if (!gradeMap.value[item.grade.id]) {
          gradeMap.value[item.grade.id] = [];
        }
        gradeMap.value[item.grade.id].push(item.id);

        if (!gradeClassMap.value[item.id]) {
          gradeClassMap.value[item.id] = {};
        }
        gradeClassMap.value[item.id] = { id: item.id, name: item.name };
      });
    } catch (error) {
      /**/
    }
  };

  const isReady = ref(false);

  // 生命周期钩子
  onMounted(async () => {
    loadData();
    await loadTimeTable();
    await loadGradeClass();
    window.addEventListener('weeklyScheduleUpdated', handleWeeklyScheduleUpdate);
    window.addEventListener('constraintsUpdated', handleConstraintsUpdate);
    isReady.value = true;
  });

  onUnmounted(() => {
    window.removeEventListener('weeklyScheduleUpdated', handleWeeklyScheduleUpdate);
    window.removeEventListener('constraintsUpdated', handleConstraintsUpdate);
  });
</script>

<style scoped>
  .schedule-overview {
    background: #f8fafc;
  }

  /* 统一卡片样式 */
  .metric-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    transition:
      transform 0.2s,
      box-shadow 0.2s;
    cursor: pointer; /* 保留指针样式，因为卡片仍然可点击打开模态框 */
  }

  .metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .metric-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px; /* 调整为一致 */
    height: 36px; /* 调整为一致 */
    flex-shrink: 0;
    /* 移除 mr-4，在父级使用 gap 控制间距 */
  }

  .warning-tag {
    padding: 0 4px;
    height: 18px;
    line-height: 18px;
    font-size: 13px; /* 调整为 text-xs */
  }

  /* 使用:deep()替代!important */
  :deep(.warning-tag.arco-tag-size-small) {
    background-color: #fef2f2; /* red-50 */
    border-color: #fecaca; /* red-200 */
    color: #ef4444; /* red-500 */
  }

  :deep(.warning-tag.arco-tag-size-small:hover) {
    background-color: #fee2e2; /* red-100 */
  }

  /* 详情模态框样式 */
  .detail-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 8px; /* 为滚动条留出空间 */
  }

  .detail-item {
    padding: 12px;
    background: #f9fafb; /* gray-50 */
    border-radius: 4px;
    display: flex; /* 改回 flex */
    justify-content: space-between; /* 让名字和详情分开 */
    align-items: center; /* 垂直居中 */
    /* gap: 4px; */ /* 移除内部gap */
  }

  .detail-content {
    display: flex;
    flex-direction: column; /* 保持名字和详情的纵向排列 */
    gap: 6px;
    /* flex-grow: 1; */ /* 移除，让其自然宽度 */
  }

  .detail-name {
    font-weight: 500;
    color: #111827; /* gray-900 */
    font-size: 14px;
  }

  .detail-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .detail-schedule {
    color: #6b7280; /* gray-500 */
    font-size: 13px;
    text-align: right; /* 让可用时间靠右 */
    white-space: nowrap; /* 防止换行 */
  }

  .detail-section {
    padding: 16px;
    background: #f9fafb; /* gray-50 */
    border-radius: 4px;
  }

  .detail-header {
    font-weight: 500;
    color: #111827; /* gray-900 */
    font-size: 15px;
    margin-bottom: 12px;
  }

  .detail-info-row {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .detail-label {
    color: #6b7280; /* gray-500 */
    font-size: 14px;
  }

  .excluded-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px; /* 调整标签间距 */
  }

  .mt-2 {
    margin-top: 0.5rem;
  }
  .mt-4 {
    margin-top: 1rem;
  }

  .classroom-tag {
    background: #eef2ff; /* indigo-50 */
    color: #4f46e5; /* indigo-600 */
    border: none;
  }

  .capacity-text {
    color: #6b7280; /* gray-500 */
    font-size: 13px;
  }

  .teacher-tag {
    transition: all 0.2s ease;
  }

  .teacher-tag:hover {
    transform: translateY(-1px);
  }

  .no-teachers {
    color: #9ca3af; /* gray-400 */
    font-size: 13px;
    font-style: italic;
  }

  @media (max-width: 768px) {
    .metric-icon {
      width: 32px;
      height: 32px;
    }
    .detail-item {
      flex-direction: column; /* 在小屏幕上堆叠 */
      align-items: flex-start;
      gap: 8px;
    }
    .detail-schedule {
      text-align: left; /* 在小屏幕上左对齐 */
    }
  }
</style>
