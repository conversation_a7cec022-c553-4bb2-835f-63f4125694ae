<script setup lang="ts">
  import { request } from '@repo/infrastructure/request';
  import { computed, onMounted, ref, watch } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { PROJECT_URLS } from '@repo/env-config';
  import TimetableByClass from '@/views/manage/components/timetable/timetableByClass.vue';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { useTeacherStore, useUserMenuStore } from '@repo/infrastructure/store';
  import TimetableByTeacher from '@/views/manage/components/timetable/timetableByTeacher.vue';
  import ManualCreateTimetableModal from '@/views/manage/components/timetable/manualCreateTimetableModal.vue';
  import RecycleBinButton from '@repo/ui/components/table/recycleBinButton.vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const route = useRoute();
  const router = useRouter();
  const schoolCourseStore = useSchoolCourseStore();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const ready = ref(false);
  const searchKeywords = ref('');

  const periods = ref([]);
  const currentTimetable = ref<any>(null);
  const viewBy = ref<'gradeClass' | 'teacher'>('gradeClass');
  const currentPeriod = ref<string>('');

  const allSchools = ref<any[]>([]);
  const allGrades = ref<any[]>([]);
  const allGradeClasses = ref<any[]>([]);
  const currentSchool = ref<any>(null);

  const grades = ref<any[]>([]);
  const allLessons = ref<any[]>([]);
  const gradeClasses = ref<any[]>([]);
  const teachers = ref<any[]>([]);

  const loading = ref(false);

  const gradesMap = computed(() => {
    return grades.value.reduce((acc, grade) => {
      acc[grade.id] = grade;
      return acc;
    }, {});
  });
  const allLessonsMap = computed(() => {
    return allLessons.value.reduce((acc, lesson) => {
      acc[lesson.id] = lesson;
      return acc;
    }, {});
  });
  const gradeClassesMap = computed(() => {
    return gradeClasses.value.reduce((acc, gradeClass) => {
      acc[gradeClass.id] = gradeClass;
      return acc;
    }, {});
  });
  const teachersMap = computed(() => {
    return teachers.value.reduce((acc, teacher) => {
      acc[teacher.id] = teacher;
      return acc;
    }, {});
  });

  const loadTimetable = async (period) => {
    if (!period || !currentSchool.value) {
      currentTimetable.value = null;
      return;
    }
    loading.value = true;
    try {
      const { data: timetable } = await request('/teacher/timetable/detail', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          period,
          schoolId: currentSchool.value,
        },
      });
      currentTimetable.value = timetable;
    } finally {
      loading.value = false;
    }
  };

  const handleActiveChange = () => {
    /* if (currentTimetable.value.active) {
      return;
    } */
    Modal.confirm({
      title: '提示',
      content: '是否确认切换当前使用的课表？',
      onOk: async () => {
        Message.loading('正在切换课表，请稍后');
        await request(`/teacher/timetable/active/${currentTimetable.value.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'POST',
          params: {
            schoolId: currentSchool.value,
          },
        });
        await loadTimetable(currentTimetable.value.period);
      },
    });
  };

  const getGrades = async () => {
    const { data } = await request('/teacher/schoolGrade', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        pageSize: 999,
        graduate: false,
        orgNature: menuInfo.app?.label,
      },
    });

    // const schoolIdsSet = new Set(data.items?.map((grade) => grade.school?.id));
    // if (schoolIdsSet.size > 1) {
    //   Modal.error({
    //     title: '错误',
    //     content: '您账号下存在多个学校，无法进行排课',
    //     onOk: () => {
    //       router.back();
    //     },
    //   });
    // }

    const schoolsMap = data.items.reduce((acc, grade) => {
      acc[grade.school.id] = {
        label: grade.school.name,
        value: grade.school.id,
        boId: grade.school.branchOfficeId,
      };
      return acc;
    }, {});

    allGrades.value = data.items || [];
    allSchools.value = Object.values(schoolsMap);
  };

  const getGradeClasses = async () => {
    const { data } = await request('/resourceRoom/gradeClass', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        'pageSize': 999,
        'orgNature': menuInfo.app?.label,
        'grade.graduate': false,
      },
    });

    allGradeClasses.value = data.items || [];
  };

  const getLessons = async () => {
    allLessons.value = await schoolCourseStore.getSchoolCourses();
  };

  const getTeachers = async () => {
    const teacherStore = useTeacherStore();
    teachers.value = await teacherStore.getTeachersList();
  };

  const handleSave = async (callback) => {
    await request(`/teacher/timetable/${currentTimetable.value.id}`, {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'PUT',
      data: currentTimetable.value,
    });
    Message.success('课表更新成功');

    if (callback) {
      callback();
    }
  };

  const manualCreateVisible = ref(false);
  const handleShowManualCreate = () => {
    manualCreateVisible.value = true;
  };

  const loadPeriods = async (schoolId: number) => {
    if (!schoolId) {
      currentPeriod.value = '';
      return;
    }
    const { data: rawPeriods } = await request('/teacher/timetable/periods', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        schoolId,
      },
    });

    if (rawPeriods?.length) {
      periods.value = rawPeriods || [];
      // eslint-disable-next-line prefer-destructuring
      currentPeriod.value = rawPeriods[0];
    }
  };

  const handleDeleteCurrent = async () => {
    Modal.confirm({
      title: '提示',
      content: '是否确认删除本学期课表？',
      onOk: async () => {
        await request(`/teacher/timetable/${currentTimetable.value.id}`, {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'DELETE',
        });
        Message.success('删除成功');
        currentTimetable.value = null;

        await loadPeriods(currentSchool.value);
      },
    });
  };

  const schema = ref(null);

  onMounted(async () => {
    await Promise.all([getGrades(), getGradeClasses(), getLessons()]);
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/timetable');

    if (allSchools.value.length === 1) {
      // eslint-disable-next-line prefer-destructuring
      currentSchool.value = allSchools.value[0]?.value;
    }

    ready.value = true;
  });

  const handleManualArrangeCreated = (data) => {
    currentTimetable.value = data;
    currentPeriod.value = data.period;
  };

  watch(
    () => currentSchool.value,
    async (val) => {
      grades.value = allGrades.value.filter((grade) => grade.school.id === currentSchool.value);
      const gradeIds = grades.value.map((grade) => grade.id);
      gradeClasses.value = allGradeClasses.value.filter((gradeClass) => gradeIds.includes(gradeClass.grade.id));
      if (!val) {
        currentPeriod.value = '';
        currentTimetable.value = null;
        return;
      }
      await Promise.all([getTeachers(), loadPeriods(val)]);
    },
  );

  watch(
    () => currentPeriod.value,
    async (val) => {
      await loadTimetable(val);
    },
  );
</script>

<template>
  <a-card v-if="ready">
    <template #title>
      <div class="flex justify-between items-center">
        <a-space>
          <a-select v-model="currentSchool" size="mini" class="w-48" placeholder="请选择学校" :options="allSchools" />
          <a-select
            v-if="currentSchool"
            v-model="currentPeriod"
            size="mini"
            class="w-48"
            placeholder="请选择学期"
            :options="periods"
          />
          <a-space v-if="currentTimetable">
            <a-select v-model="viewBy" size="mini" class="w-36" placeholder="请选择">
              <a-option value="gradeClass">按班级查看</a-option>
              <a-option value="teacher">按教师查看</a-option>
            </a-select>
            <a-input
              v-if="viewBy === 'teacher'"
              v-model="searchKeywords"
              size="mini"
              allow-clear
              placeholder="输入教师姓名搜索"
            />
          </a-space>
        </a-space>
        <a-space>
          <a-button v-if="currentSchool" size="mini" @click="handleShowManualCreate">
            <template #icon>
              <IconPlus />
            </template>
            手动创建课表
          </a-button>
          <a-button
            v-if="currentTimetable?.id && currentSchool"
            :disabled="currentTimetable.active"
            size="mini"
            status="danger"
            type="outline"
            @click="handleDeleteCurrent"
          >
            <template #icon>
              <IconDelete />
            </template>
            删除此课表
          </a-button>
          <recycle-bin-button
            v-if="schema"
            :visible-columns="['period', 'taskId', 'createdDate', 'createdBy']"
            :schema="schema"
            @refresh="getTeachers"
          />
          <a-switch
            v-if="currentTimetable"
            :model-value="currentTimetable.active"
            size="medium"
            type="round"
            checked-color="green"
            checked-text="当前使用"
            unchecked-text="未启用"
            @change="handleActiveChange"
          />
        </a-space>
      </div>
    </template>
    <a-skeleton v-if="loading" animation>
      <a-skeleton-line :rows="5" />
    </a-skeleton>
    <div v-else>
      <timetable-by-class
        v-if="viewBy === 'gradeClass' && currentTimetable"
        v-model:timetable="currentTimetable"
        :lessons-map="allLessonsMap"
        :teachers-map="teachersMap"
        :grade-classes="gradeClasses"
        :grades="grades"
        @save="handleSave"
      />
      <timetable-by-teacher
        v-else-if="viewBy === 'teacher' && currentTimetable"
        :timetable="currentTimetable"
        :teachers="teachers"
        :grade-classes-map="gradeClassesMap"
        :keyword="searchKeywords"
      />
      <a-empty v-if="!currentTimetable?.id"></a-empty>
    </div>
    <manual-create-timetable-modal
      v-if="currentSchool"
      v-model:visible="manualCreateVisible"
      :grades="grades"
      :grade-classes="gradeClasses"
      :school-id="currentSchool"
      @ok="handleManualArrangeCreated"
    />
  </a-card>
  <a-card v-else>
    <a-skeleton animation>
      <a-skeleton-line :rows="5" />
    </a-skeleton>
  </a-card>
</template>
