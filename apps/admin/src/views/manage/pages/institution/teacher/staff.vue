<script setup lang="ts">
  import UserManage from '@repo/components/org/userManage.vue';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { ref } from 'vue';

  const menuStore = useUserMenuStore();

  const queryParams = ref({
    nature: menuStore.getCurrentOrgNature(),
  });
</script>

<template>
  <user-manage module-name="机构人员管理" :query-params="queryParams" />
</template>
