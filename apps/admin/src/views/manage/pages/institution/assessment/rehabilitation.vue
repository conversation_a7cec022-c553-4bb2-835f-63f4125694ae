<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';

  const schema = ref(null);

  const queryParams = {
    sort: '-id',
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/rehabilitation/rehabilitationAssessment');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="康复评估"
    :schema="schema"
    :default-query-params="queryParams"
    :table-actions-property="{ visibleComponents: ['refresh', 'quickSearch'] }"
    :visible-columns="['institution', 'student', 'assessmentDate', 'participants', 'attachments', 'createdBy']"
  />
</template>

<style scoped lang="scss"></style>
