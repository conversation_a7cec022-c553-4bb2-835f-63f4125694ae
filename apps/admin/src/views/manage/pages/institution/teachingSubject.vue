<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useUserMenuStore } from '@repo/infrastructure/store';

  const schema = ref(null);

  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo();

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/teacher/schoolCourse');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="教学科目"
    :schema="schema"
    :default-edit-value="{ orgNature: menuInfo.app.label }"
    :default-query-params="{ sort: '-id', orgNature: menuInfo.app.label }"
    :visible-columns="['name', 'simpleName', 'required', 'groupTeachers']"
  >
  </table-with-modal-form>
</template>
