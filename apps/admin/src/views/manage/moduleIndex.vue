<script setup lang="ts">
  import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { computed, defineAsyncComponent, nextTick, onMounted, ref } from 'vue';
  import subModulesComponents from '@/views/manage/pages';

  const route = useRoute();
  const router = useRouter();
  const menuStore = useUserMenuStore();
  const menuInfo = ref<any>(menuStore.getCurrentMenuInfo(route));
  const ready = ref(false);

  onBeforeRouteUpdate((to, from, next) => {
    menuInfo.value = menuStore.getCurrentMenuInfo(to);
    next();
  });
  const subModuleComponent = computed(() => {
    const currentMenuInfo = menuStore.getCurrentMenuInfo(route);
    if (!currentMenuInfo?.subModules?.length) {
      const path = `./pages/${menuInfo.value.app?.key}/${menuInfo.value.module?.key}.vue`;
      if (subModulesComponents[path]) {
        return defineAsyncComponent(subModulesComponents[path]);
      }
    }
    return null;
  });

  onMounted(async () => {
    if (!menuInfo.value.subModules?.length && menuInfo.value.module?.children?.length > 0) {
      await router.push(menuInfo.value.module.children[0].link as string);
    }

    await nextTick(() => {
      ready.value = true;
    });
  });
</script>

<template>
  <div v-if="ready">
    <router-view v-if="menuInfo?.subModules?.length" />
    <component :is="subModuleComponent" v-else-if="subModuleComponent" />
  </div>
</template>

<style scoped lang="scss"></style>
