<script setup lang="ts">
  import { onBeforeRouteUpdate, useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { computed, defineAsyncComponent, ref } from 'vue';
  import subModulesComponents from '@/views/manage/pages';
  import { Message } from '@arco-design/web-vue';

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = ref<any>(menuStore.getCurrentMenuInfo(route));

  const currentModule = computed(() => {
    let menus = menuInfo.value.module?.children || [];
    let module: any = {};
    for (let i = 0; i < menuInfo.value.subModules?.length; i += 1) {
      module = menus.find((m: any) => m.key === menuInfo.value.subModules[i]);
      menus = module?.children || [];
    }
    return module;
  });

  const moduleComponent = computed(() => {
    if (currentModule.value?.component) {
      return defineAsyncComponent(() => import(currentModule.value?.component));
    }
    const currentMenuInfo = menuStore.getCurrentMenuInfo(route);
    const path = `./pages/${currentMenuInfo.app?.key}/${currentMenuInfo.module?.key}/${currentMenuInfo.subModules?.join('/')}.vue`;
    if (!subModulesComponents[path]) {
      Message.error(`模块不存在: ${path}`);
      return null;
    }
    return defineAsyncComponent(subModulesComponents[path]);
  });

  onBeforeRouteUpdate((to) => {
    menuInfo.value = menuStore.getCurrentMenuInfo(to);
  });
</script>

<template>
  <component :is="moduleComponent" />
</template>

<style scoped lang="scss"></style>
