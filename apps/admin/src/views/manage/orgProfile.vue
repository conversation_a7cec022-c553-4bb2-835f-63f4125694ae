<script lang="ts" setup>
  import { useUserStore } from '@repo/infrastructure/store';
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { CrudForm } from '@repo/ui/components';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { Message } from '@arco-design/web-vue';
  import { useRouter } from 'vue-router';

  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();
  const schema = ref();
  const formData = ref<any>({});
  const visibleColumns = ['itemAttachments', 'region', 'address'];

  const handleSaveProfile = async () => {
    setLoading(true);

    try {
      await request('/resourceCenter/fusionSchool/profile', {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...formData.value,
        },
      });

      userStore.setInfo({
        ...userStore.userInfo,
        ...formData.value,
      });

      Message.success('保存单位资料成功');
    } finally {
      setLoading(false);
    }
  };

  const { userInfo } = userStore;
  const router = useRouter();

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/fusionSchool');
    formData.value = {
      id: userInfo?.branchOffice?.school?.id,
    };
  });
</script>

<template>
  <a-card :title="userInfo?.branchOffice?.school?.name" class="mx-auto mt-2" size="small" style="width: 960px">
    <template #extra>
      <a-space>
        <a-button type="primary" size="mini" :loading="loading" @click="handleSaveProfile">
          <template #icon>
            <IconCheck />
          </template>
          保存单位资料
        </a-button>
        <a-button size="mini" @click="() => router.back()">返回</a-button>
      </a-space>
    </template>
    <crud-form
      v-if="schema"
      v-model="formData"
      :visible-columns="visibleColumns"
      :show-actions="false"
      :schema="schema"
    />
  </a-card>
</template>
