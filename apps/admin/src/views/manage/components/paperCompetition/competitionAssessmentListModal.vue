<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable } from '@repo/ui/components/table';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    paperWork: {
      type: Object as PropType<any>,
      required: true,
    },
    competition: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);
  const schema = ref(null);
  const visibleColumns = [
    'expertName',
    'expertMobile',
    'expertOrg',
    'submitStatus',
    'totalScore',
    'timeSpend',
    'modifiedDate',
  ];

  const defaultQueryParams = computed(() => ({
    paperCompetitionId: props.competition?.id,
    entryId: props.paperWork?.id,
  }));

  const expertMap = computed(() => {
    const map = {};
    props.competition?.experts?.forEach((expert) => {
      map[expert.mobile] = expert;
    });
    return map;
  });

  const expandable = ref({
    title: '详情',
    width: 100,
  });

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/paper/competitionEntryAssessmentResult');
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen>
    <template #title> {{ paperWork?.subject }} 评审记录 </template>
    <crud-table
      v-if="schema && paperWork && modalVisible"
      ref="tableRef"
      :expandable="expandable"
      :default-query-params="defaultQueryParams"
      class="mt-2"
      :schema="schema"
      :show-row-actions="false"
      :visible-columns="visibleColumns"
    >
      <template #custom-column-expertName="{ record }">
        {{ expertMap[record.expertMobile]?.name }}
      </template>
      <template #custom-column-expertMobile="{ record }">
        {{ record.expertMobile }}
      </template>
      <template #custom-column-expertOrg="{ record }">
        {{ expertMap[record.expertMobile]?.organization }}
      </template>
      <template #expand-row="{ record }">
        <div v-for="(criterion, i) in competition.criterionList" :key="i" class="mb-4 mt-2">
          {{ i + 1 }}. {{ criterion.criterion }}
          <div
            v-for="(item, j) in criterion.children"
            :key="j"
            class="mt-1 flex py-1 px-2 gap-2 text-sm hover:bg-white"
          >
            <div class="flex-1"
              >{{ i + 1 }}.{{ j + 1 }}
              <span class="ml-2">{{ item.criterion }}</span>
            </div>
            <div class="w-48">推荐分数：{{ item.recommendScore }}</div>
            <div class="w-48">评分：{{ record.scores[i][j] }}</div>
          </div>
        </div>
      </template>
    </crud-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
