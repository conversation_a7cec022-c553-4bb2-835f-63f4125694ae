<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { listTableInput } from '@repo/ui/components/form/inputComponents';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { CrudTable } from '@repo/ui/components/table';
  import { secondsFormatter } from '@repo/ui/components/utils/utils';

  const props = defineProps({
    modelValue: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      required: true,
    },
  });

  const loading = ref(false);
  const emit = defineEmits(['update:visible', 'update:modelValue', 'ok']);
  const schema = ref<any>(null);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const experts = ref<any[]>([]);
  const reviewStatisticsData = ref([]);

  const fieldsMap = {
    expertName: { key: 'expertName', label: '专家姓名' },
    expertMobile: { key: 'expertMobile', label: '专家手机号' },
    count: { key: 'count', label: '已提交评审数量' },
    orgName: { key: 'orgName', label: '所属机构' },
    timeSpend: {
      key: 'timeSpend',
      label: '用时合计',
      displayProps: {
        toDisplay: (value: number) => {
          return value ? secondsFormatter(value) : '-';
        },
      },
    },
  };
  const reviewStatisticsSchema = {
    api: '/paper/competitionEntryAssessmentResult/expertReviewStatistics',
    fieldsMap,
    fields: Object.values(fieldsMap),
    schemaFields: Object.values(fieldsMap),
  };

  const handleOpen = async () => {
    modalVisible.value = true;
    experts.value = props.modelValue.experts || [];

    const { data } = await request(`/paper/competitionEntryAssessmentResult/expertReviewStatistics`, {
      params: {
        competitionId: props.modelValue.id,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    const expertsMap = experts.value.reduce((prev, current) => {
      prev[current.mobile] = current;
      return prev;
    }, {});

    reviewStatisticsData.value =
      data
        ?.sort((a, b) => {
          return a.count - b.count;
        })
        .map((item) => {
          const expert = expertsMap[item.expertMobile];
          return {
            ...item,
            orgName: expert?.organization,
            expertName: expert?.name,
            expertMobile: expert?.mobile,
          };
        }) || [];
  };

  const handleOk = async () => {
    loading.value = true;
    const expertsList = experts.value || [];
    const hasEmptyExpert = expertsList.some((expert) => {
      return !expert.name || !expert.mobile;
    });
    if (hasEmptyExpert) {
      Message.error('请填写完整专家信息');
      loading.value = false;
      return false;
    }
    try {
      const data = {
        ...props.modelValue,
        experts: expertsList,
      };
      await request(`/paper/paperCompetition/${props.modelValue.id}`, {
        method: 'PUT',
        data,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('专家设置成功');
      emit('update:modelValue', data);
      emit('ok');
      modalVisible.value = false;

      return true;
    } finally {
      loading.value = false;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/paper/expert');
  });
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="评审专家设置"
    fullscreen
    :on-before-ok="handleOk"
    :ok-loading="loading"
    @open="handleOpen"
  >
    <list-table-input v-if="schema" v-model="experts" :schema-field="schema" />
    <div v-if="reviewStatisticsData?.length" class="mt-8">
      <a-divider :margin="16">评审统计</a-divider>
      <crud-table
        :schema="reviewStatisticsSchema"
        :pagination="false"
        :auto-load="false"
        :show-row-actions="false"
        :visible-columns="['orgName', 'expertName', 'expertMobile', 'count', 'timeSpend']"
        :data="reviewStatisticsData"
      />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
