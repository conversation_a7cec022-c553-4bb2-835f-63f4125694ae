<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  import { cloneDeep } from 'lodash';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Object,
      required: true,
    },
  });

  const { loading, setLoading } = useLoading();
  const emit = defineEmits(['update:visible', 'update:modelValue', 'ok']);
  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const criterionList = ref<any[]>([]);

  const handleOk = async () => {
    setLoading(true);
    try {
      const data = {
        ...props.modelValue,
        criterionList: criterionList.value || [],
      };
      await request(`/paper/paperCompetition/updateSettings/${props.modelValue.id}`, {
        method: 'PUT',
        data,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('评分标准设置成功');
      emit('update:modelValue', data);
      emit('ok');
      modalVisible.value = false;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setLoading(false);
    criterionList.value = [];
    modalVisible.value = false;
  };

  const handleAdd = (parent?: any) => {
    if (parent) {
      parent.children = parent.children || [];
      parent.children.push({
        criterion: '',
        recommendScore: 0,
        sort: 99,
      });
      return;
    }
    criterionList.value.push({
      criterion: '',
      recommendScore: 0,
      sort: 99,
    });
  };

  const handleSort = (val: number, item: any, list: any[]) => {
    item.sort = val;
    list.sort((a, b) => a.sort - b.sort);
  };

  const handleDelete = (index: number, parentIndex?: number) => {
    if (!parentIndex && parentIndex !== 0) {
      criterionList.value.splice(index, 1);
    } else {
      criterionList.value[parentIndex].children.splice(index, 1);
    }
  };

  const handleOpen = () => {
    criterionList.value = cloneDeep(props.modelValue.criterionList || []);
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="评分标准设置"
    :on-before-ok="handleOk"
    :ok-loading="loading"
    :width="800"
    @open="handleOpen"
    @cancel="handleCancel"
  >
    <div> 主题：{{ props.modelValue.name }} </div>

    <a-button type="primary" class="mt-4" size="mini" @click="() => handleAdd()">添加一级指标</a-button>
    <div class="flex gap-2 items-center mt-4 text-center">
      <div class="flex-1 bg-slate-100">指标</div>
      <div class="w-32 bg-slate-100">参考分值</div>
      <!--      <div class="w-32 bg-slate-100">显示顺序</div>-->
      <div class="w-24 bg-slate-100">操作</div>
    </div>
    <div v-for="(fl, i) in criterionList" :key="i" class="mt-4 flex flex-col gap-2">
      <div class="flex gap-2 items-center">
        <div class="flex gap-2 justify-start items-start flex-1">
          {{ i + 1 }}、
          <a-input v-model="fl.criterion" size="mini" class="flex-1"> </a-input>
        </div>
        <!--        <a-input-number-->
        <!--          :model-value="fl.sort"-->
        <!--          size="mini"-->
        <!--          placeholder="显示顺序"-->
        <!--          mode="button"-->
        <!--          class="w-32"-->
        <!--          @change="(val) => handleSort(val, fl, criterionList)"-->
        <!--        />-->
        <a-space class="w-24 justify-end">
          <a-tooltip content="添加下级指标">
            <a-button size="mini" @click="() => handleAdd(fl)">
              <template #icon>
                <IconPlus />
              </template>
            </a-button>
          </a-tooltip>
          <a-popconfirm
            content="确定要删除这个一级指标吗？（其下级指标将被一同删除）"
            class="w-64"
            @ok="() => handleDelete(i)"
          >
            <a-button size="mini" status="danger" type="outline">
              <template #icon>
                <IconDelete />
              </template>
            </a-button>
          </a-popconfirm>
        </a-space>
      </div>
      <div v-for="(sl, j) in fl.children" :key="j" class="flex gap-2 items-center ml-4">
        <div class="flex gap-2 justify-start items-start flex-1">
          {{ i + 1 }}.{{ j + 1 }}
          <a-textarea v-model="sl.criterion" size="mini" class="flex-1"> </a-textarea>
        </div>
        <div class="w-32">
          <a-input-number v-model="sl.recommendScore" size="mini" mode="button" placeholder="参考分值" />
        </div>
        <!--        <a-input-number-->
        <!--          :model-value="sl.sort"-->
        <!--          size="mini"-->
        <!--          placeholder="显示顺序"-->
        <!--          mode="button"-->
        <!--          class="w-32"-->
        <!--          @change="(val) => handleSort(val, sl, fl.children)"-->
        <!--        />-->
        <a-space class="w-24 justify-end">
          <a-popconfirm content="确定要删除这个一级指标吗？" @ok="() => handleDelete(j, i)">
            <a-button size="mini" status="danger" type="outline">
              <template #icon>
                <IconDelete />
              </template>
            </a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>
    <div v-if="!criterionList?.length" class="p-4">
      <a-empty description="暂无评分标准，请先添加" />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
