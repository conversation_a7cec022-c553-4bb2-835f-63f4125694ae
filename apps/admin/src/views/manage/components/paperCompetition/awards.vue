<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    modelValue: {
      type: Object,
      required: true,
    },
  });

  const { loading, setLoading } = useLoading();
  const emit = defineEmits(['update:visible', 'update:modelValue', 'ok']);
  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const awards = ref<any[]>(props.modelValue.awards || []);

  const handleOk = async () => {
    setLoading(true);
    try {
      const data = {
        ...props.modelValue,
        awards: awards.value || [],
      };
      await request(`/paper/paperCompetition/${props.modelValue.id}`, {
        method: 'PUT',
        data,
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });
      Message.success('奖项设置成功');
      emit('update:modelValue', data);
      emit('ok');
      modalVisible.value = false;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setLoading(false);
    awards.value = [];
    modalVisible.value = false;
  };

  const handleAdd = () => {
    awards.value.push({
      count: 1,
    });
  };

  const handleDelete = (index: number) => {
    awards.value.splice(index, 1);
  };

  watch(
    () => props.modelValue,
    (value) => {
      awards.value = value.awards || [];
    },
    {
      immediate: true,
    },
  );
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="奖项设置"
    :on-before-ok="handleOk"
    :ok-loading="loading"
    @cancel="handleCancel"
  >
    <div> 主题：{{ props.modelValue.name }} </div>
    <div class="p-4">
      <a-row v-for="(award, index) in awards" :key="index" :gutter="20">
        <a-col :span="12">
          <a-form-item :label="`奖项 [${index + 1}]`">
            <a-input v-model.trim="award.name" size="mini" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="奖项数量">
            <a-input-number v-model.trim="award.count" mode="button" size="mini" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item :label="`描述 [${index + 1}]`">
            <div class="w-full flex gap-2">
              <a-input v-model.trim="award.description" class="flex-1" size="mini" />
              <a-button type="primary" status="danger" size="mini" @click="() => handleDelete(index)">删除</a-button>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
      <a-empty v-if="!awards.length" description="暂无奖项，请先添加" />
    </div>
    <a-button type="primary" size="mini" @click="handleAdd">添加奖项</a-button>
  </a-modal>
</template>

<style scoped lang="scss"></style>
