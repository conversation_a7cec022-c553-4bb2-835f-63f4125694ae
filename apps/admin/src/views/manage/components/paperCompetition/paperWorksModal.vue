<script setup lang="ts">
  import { computed, nextTick, onMounted, PropType, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable } from '@repo/ui/components/table';
  import CompetitionAssessmentListModal from '@/views/manage/components/paperCompetition/competitionAssessmentListModal.vue';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import PaperWorkConentViewModal from '@/views/manage/components/paperCompetition/paperWorkConentViewModal.vue';
  import { loadPrintTemplate, Preview } from '@repo/printer';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    competition: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);
  const schema = ref(null);
  const assessmentsVisible = ref(false);
  const contentViewVisible = ref(false);
  const currentPaperWork = ref(null);
  const tableRef = ref(null);
  const printPreviewVisible = ref(false);
  const previewRef = ref(null);
  const printTemplate = ref({});

  const visibleColumns = computed(() => {
    if (props.competition.type === '论文类') {
      return [
        'type',
        'subject',
        'authors',
        'guideTeacher',
        'averageScore',
        'award',
        'duplication',
        'createdDate',
        'createdBy',
      ];
    }
    return ['type', 'subject', 'authors', 'guideTeacher', 'averageScore', 'award', 'createdDate', 'createdBy'];
  });

  const defaultQueryParams = computed(() => ({
    paperCompetitionId: props.competition?.id,
    submitted: true,
    sort: '-averageScore',
  }));

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleRowAction = (action, record) => {
    if (action.key === 'assessments') {
      currentPaperWork.value = record;
      assessmentsVisible.value = true;
    } else if (action.key === 'viewContent') {
      currentPaperWork.value = record;
      contentViewVisible.value = true;
    }
  };

  const handleSetAward = async (record, award) => {
    Message.loading('正在设置奖项...');
    await request(`/paper/paperCompetitionEntries/${record.id}/award`, {
      method: 'PUT',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      data: {
        ...record,
        award,
      },
    });

    Message.clear();
    Message.success('设置奖项成功');
    tableRef.value?.loadData();
  };

  const competitionAssessmentFinished = computed(() => {
    const [, endTime] = (props.competition?.assessmentDateRange || []).map((item) => {
      return item ? new Date(item) : null;
    });

    return endTime && endTime < new Date();
  });

  const handlePrint = async (record) => {
    Message.info('正在加载打印模板...');
    const template = await loadPrintTemplate('PaperCompetitionAward', record.paperCompetitionId);
    const printData = {
      competitionSubject: props.competition?.name,
      type: record.type,
      subject: record.subject,
      authors: record.authors?.join('、'),
    };

    printTemplate.value = template?.content;
    printPreviewVisible.value = true;
    await nextTick(() => {
      previewRef.value?.handlePrint([printData]);
    });
  };

  const handleNotify = async (record) => {
    await request(`/paper/paperCompetitionEntries/${record.id}/notify`, {
      method: 'POST',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    Message.success('通知成功');
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/paper/paperCompetitionEntries');
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen>
    <template #title> {{ competition?.name }} 参赛作品 </template>
    <a-space>
      <a-button size="mini" @click="() => tableRef.loadData()">
        <template #icon>
          <IconRefresh />
        </template>
        刷新
      </a-button>
    </a-space>
    <crud-table
      v-if="schema && competition && modalVisible"
      ref="tableRef"
      :default-query-params="defaultQueryParams"
      class="mt-2"
      :schema="schema"
      :visible-columns="visibleColumns"
      @row-action="handleRowAction"
    >
      <template #custom-column-award="{ record }">
        <div v-if="competitionAssessmentFinished">
          <a-space>
            <a-popover trigger="click" class="w-64">
              <a-button size="mini" :type="record.award ? 'outline' : 'secondary'">
                {{ record.award?.trim() || '颁奖' }}
              </a-button>
              <template #content>
                <a-space class="flex-wrap">
                  <a-button
                    v-for="(awardItem, i) in competition.awards"
                    :key="i"
                    size="mini"
                    class="mb-2"
                    @click="() => handleSetAward(record, awardItem.name)"
                  >
                    {{ awardItem.name }} ({{ awardItem.count }}个)
                  </a-button>
                </a-space>
              </template>
            </a-popover>
            <a-tooltip content="打印获奖证书">
              <a-button v-if="record.award?.trim()" size="mini" @click="() => handlePrint(record)">
                <template #icon>
                  <IconPrinter />
                </template>
              </a-button>
            </a-tooltip>
            <a-popconfirm
              v-if="record.award?.trim()"
              :content="record.notifySent ? '已通知过，确定要再次通知吗？' : '确定要通知获奖者吗？'"
              @ok="() => handleNotify(record)"
            >
              <a-button v-if="record.award?.trim()" :type="record.notifySent ? 'secondary' : 'outline'" size="mini">
                <template #icon>
                  <IconNotification />
                </template>
              </a-button>
            </a-popconfirm>
            <a-popconfirm
              v-if="record.award?.trim()"
              content="确定要取消这个奖项吗？"
              @ok="() => handleSetAward(record, '')"
            >
              <a-button size="mini">
                <template #icon>
                  <IconClose />
                </template>
              </a-button>
            </a-popconfirm>
          </a-space>
        </div>
        <div v-else>评审尚未结束</div>
      </template>
      <template #custom-column-duplication="{ record }">
        <div v-if="!record.duplication?.similarity"> 未发现重复 </div>
        <a-popover v-else class="w-80">
          <div :class="{ 'text-red-500 font-medium': record.duplication?.similarity > 0.6 }"
            >相似率: {{ record.duplication?.similarity * 100 }}%</div
          >
          <template #content>
            <a-space direction="vertical">
              <div
                >相似率：{{ record.duplication?.similarity * 100 }}%
                <a-popover content="如相似率超过80%则使用第一个超过80%的结果">
                  <IconQuestionCircleFill class="cursor-pointer" />
                </a-popover>
              </div>
              <div>作品作者：{{ record.duplication?.relatedAuthor?.join('、') }}</div>
              <div>相似作品：{{ record.duplication?.relatedSubject }}</div>
            </a-space>
          </template>
        </a-popover>
      </template>
    </crud-table>

    <competition-assessment-list-modal
      v-model:visible="assessmentsVisible"
      :competition="competition"
      :paper-work="currentPaperWork"
    />
    <paper-work-conent-view-modal
      v-model:visible="contentViewVisible"
      :competition="competition"
      :raw="currentPaperWork"
    />

    <preview
      v-if="printPreviewVisible"
      ref="previewRef"
      v-model:visible="printPreviewVisible"
      :template="printTemplate"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
