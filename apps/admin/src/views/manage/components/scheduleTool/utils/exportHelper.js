// eslint-disable-next-line import/prefer-default-export
export const exportJSON = (data, filename) => {
  // 将数据转换为JSON字符串
  const jsonStr = JSON.stringify(data, null, 2);

  // 创建Blob对象
  const blob = new Blob([jsonStr], { type: 'application/json' });

  // 创建下载链接
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;

  // 触发下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};
