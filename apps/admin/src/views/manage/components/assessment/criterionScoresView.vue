<script lang="ts" setup>
  import { PropType } from 'vue';

  defineProps({
    raw: {
      type: Array as PropType<any[]>,
    },
  });
</script>

<template>
  <a-table :data="raw" size="mini" :pagination="false">
    <template #columns>
      <a-table-column title="分值" :width="100">
        <template #cell="{ record }"> {{ record.score }} 分 </template>
      </a-table-column>
      <a-table-column title="支持程度">
        <template #cell="{ record }"> {{ record.supportLevel }} </template>
      </a-table-column>
      <a-table-column title="目标标准">
        <template #cell="{ record }"> {{ record.targetStandard }} </template>
      </a-table-column>
      <a-table-column title="目标分级">
        <template #cell="{ record }"> {{ record.targetLevel }} </template>
      </a-table-column>
    </template>
  </a-table>
</template>
