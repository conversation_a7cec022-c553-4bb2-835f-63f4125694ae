<script setup lang="ts">
  import { useAllBoStore } from '@repo/infrastructure/store/common';
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Modal } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { UNIT_NATURES_MAP } from '@repo/infrastructure/constants';

  const props = defineProps({
    student: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);
  const modelValue = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emit('update:visible', val);
    },
  });
  const orgNature = ref({
    key: 'normal',
    val: '普通教育学校',
  });
  const selectedBoId = ref();
  const boStore = useAllBoStore();
  const options = ref([]);
  const loading = ref(false);

  const currentType = ref<any>('RegularSchool');
  const handleResettle = async () => {
    if (!selectedBoId.value) {
      return;
    }
    const boName = options.value.find((item) => item.value === selectedBoId.value)?.label;
    Modal.confirm({
      title: '确认安置',
      content: `确认将学生【${props.student.name}】安置到【${boName}】吗？`,
      onOk: async () => {
        loading.value = true;
        try {
          await request(`/student/enrollResettlement/${props.student.id}/${currentType.value}/resettle`, {
            method: 'PUT',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            data: {
              id: selectedBoId.value,
            },
          });
          emit('update:visible', false);
          emit('ok');
        } finally {
          loading.value = false;
        }
      },
    });
  };
  const schoolCatch = ref<any>({});

  const loadSchoolOptions = async () => {
    if (schoolCatch.value?.[orgNature.value.key]) {
      options.value = schoolCatch.value?.[orgNature.value.key];
      return;
    }

    const { data: res } = await request('/org/branchOffice/simpleList', {
      method: 'get',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        nature: orgNature.value.val,
      },
    });
    schoolCatch.value[orgNature.value.key] = res.map((item) => ({
      label: item.name,
      value: item.id,
      raw: item.raw,
    }));
    options.value = schoolCatch.value?.[orgNature.value.key];
  };

  const handleSearchSchoolList = async (val: string) => {
    switch (val) {
      case 'normal':
        orgNature.value.key = val;
        orgNature.value.val = UNIT_NATURES_MAP.Normal;
        currentType.value = 'RegularSchool';
        break;
      case 'special':
        orgNature.value.key = val;
        orgNature.value.val = UNIT_NATURES_MAP.Special;
        currentType.value = 'SpacialSchool';
        break;
      default:
        orgNature.value.val = val;
        orgNature.value.val = '';
        currentType.value = 'HomeDelivery';
        break;
    }
    await loadSchoolOptions();
  };
  onMounted(async () => {
    // options.value = await boStore.getOptions();
    await loadSchoolOptions();
  });
</script>

<template>
  <a-modal
    v-bind="$attrs"
    v-model:visible="modelValue"
    title="学生安置"
    :on-before-ok="handleResettle"
    :ok-loading="loading"
  >
    <a-form-item label="安置去向">
      <a-radio-group size="small" default-value="normal" @change="handleSearchSchoolList">
        <a-radio value="normal">普通学校</a-radio>
        <a-radio value="special">特殊教育学校</a-radio>
        <a-radio value="send">送教上门</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="安置学校">
      <a-select v-model="selectedBoId" allow-search :options="options" />
    </a-form-item>
  </a-modal>
</template>

<style scoped lang="scss"></style>
