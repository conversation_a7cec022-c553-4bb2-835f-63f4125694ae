<!--自定义表单-->
<script setup lang="ts">
  import { inject, ref, onMounted } from 'vue';

  const props = defineProps({
    record: {
      type: Object,
      required: true,
    },
    schema: {
      type: Object,
      required: true,
    },
  });
  const record = ref(props.record);
  const VuePrintNext = inject('VuePrintNext');
  const ReportPage = inject('ReportPage');
  const request = inject('request');
  const PROJECT_URLS = inject('PROJECT_URLS');
  const getOrgNature = inject('getOrgNature');
  const handlePrint = async () => {
    try {
      // eslint-disable-next-line no-new
      new VuePrintNext({
        el: `#printArea`,
        popTitle: '',
        zIndex: 9999,
        printMode: 'popup',
        hide: '.no-print',
      });
    } catch (error) {
      /**/
    }
  };
  const tdStyle: any = {
    border: '1px solid black',
    height: '50px',
    // textAlign: 'center',
    verticalAlign: 'center',
    paddingLeft: '12px',
  };
  const thStyle: any = {
    border: '1px solid black',
    height: '50px',
    textAlign: 'center',
  };
  const cls = 'border border-black text-center align-middle h-[50px] px-2';

  const TFOptions = [
    { label: '是', value: '1' },
    { label: '否', value: '2' },
  ];
  const YNOptions = [
    { label: '有', value: '1' },
    { label: '无', value: '2' },
  ];
  const gotoSchoolAndFromSchool = ['家长接送', '自行上下学'];
  const getValue = (val: boolean = false): string => {
    return val ? '1' : '2';
  };
  const diseaseTypes = ['无', '癫痫', '哮喘', '心脏病', '其他'];
  const resettlementType = ['普通学校随班', '普通学校特教班', '特教学校', '送教上门'];
  const familyTeachingType = ['民主型', '放任型', '溺爱型'];
  const disLevel = ['一级', '二级', '三级', '四级'];
  const disCategory = ['智力残疾', '肢体残疾', '听力残疾', '精神残疾', '视力残疾', '言语残疾', '其他'];

  const student = ref<any>({});
  const psychologicalVisible = ref(false);
  const placementApplication = ref(null);
  const placementAssessment = ref(null);
  const psychologicalAssessment = ref({
    general: { student: { id: student.value?.id }, filledBy: null, filledDate: null },
    socialAbility: {
      sportsActivities: { timeSpent: '' },
      organizations: {},
      relationship: { siblings: '' },
      hobbies: { timeSpent: '' },
      jobs: { list: '' },
      friends: { count: '' },
      academics: {
        inSchool: '',
        subjects: {
          others: {},
        },
      },
    },
    behaviorProblems: [],
    student: { ...student.value },
  });

  const getScore = (val: string) => {
    const result = placementAssessment.value?.additionalData?.assessment?.find((item) => item.field === val);
    return result?.result?.score;
  };
  const getDescription = (val: string) => {
    const result = placementAssessment.value?.additionalData?.assessment?.find((item) => item.field === val);
    return result?.result?.discription;
  };
  const getSuggestion = (val: string) => {
    const result = placementAssessment.value?.additionalData?.assessment?.find((item) => item.field === val);
    return result?.result?.suggestion;
  };

  // 加载最新的心理评估报告
  const loadPsychologicalAssessment = async () => {
    const { data } = await request({
      url: `/specialCommittee/childrenBehaviorAssessment/getLatestByStudentId/${String(props.record?.id)}`,
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    if (data?.id) {
      psychologicalAssessment.value = data;
    }
  };
  // 加载最新的安置报告
  const loadPlacementAssessment = async () => {
    const { data } = await request({
      url: `/specialCommittee/placementReport/getLatestByStudentId/${props.record?.id}`,
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    placementAssessment.value = data;
  };

  const assessmentDetails = ref();

  // 或取最新的评估信息
  const loadAssessments = async () => {
    try {
      const { data } = await request({
        url: `/evaluation/customAssessmentSnapshot/findLatestByStudentId`,
        method: 'GET',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        params: {
          studentId: props.record?.id,
          orgNature: getOrgNature(),
        },
      });
      assessmentDetails.value = data;
    } catch (e) {
      console.error(e);
    }
  };

  interface TreeNode {
    id: number;
    name: string;
    score?: number;
    children?: TreeNode[];
    level?: number;
    rowspan?: number;
  }

  // 计算行合并数
  const getRowSpan = (node: TreeNode): number => {
    if (!node.children || node.children.length === 0) {
      return 1;
    }
    return node.children.reduce((sum, child) => sum + getRowSpan(child), 1);
  };

  // 扁平化树形数据
  const flattenData = (nodes: TreeNode[], level = 1): TreeNode[] => {
    return nodes.flatMap((node) => {
      const currentNode = {
        ...node,
        level,
        rowspan: getRowSpan(node),
      };

      const children = node.children ? flattenData(node.children, level + 1) : [];

      return [currentNode, ...children];
    });
  };
  //
  const sumTotalScore = (node: any): number => {
    const data = flattenData(node);
    let total = 0;
    data.forEach((item) => {
      total += item?.score || 0;
    });
    return total;
  };

  onMounted(async () => {
    await loadPsychologicalAssessment();
    await loadPlacementAssessment();
    await loadAssessments();
  });
  defineExpose({
    handlePrint,
  });
</script>

<!--自定义表单-->
<template>
  <!--安置申请-->
  <div id="printArea" style="width: 210mm" class="rounded-lg m-auto p-2">
    <!-- 申报信息表单 -->
    <table class="w-full">
      <tbody>
        <tr>
          <td colspan="8" :style="tdStyle">
            <div class="text-xl flex justify-center font-bold">学生基本情况</div>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">学生姓名</td>
          <td :style="tdStyle">{{ record?.name }}</td>
          <td :style="tdStyle">性别</td>
          <td :style="tdStyle">{{ record?.gender }}</td>
          <td :style="tdStyle">出生年月</td>
          <td :style="tdStyle">{{ record?.birthday }}</td>
          <td :style="tdStyle">民族</td>
          <td :style="tdStyle">{{ record?.nation }}</td>
        </tr>
        <tr>
          <td :style="tdStyle">身份证号码</td>
          <td :style="tdStyle" colspan="4">{{ record?.idCardNo }}</td>
          <td :style="tdStyle">是否是独生子女</td>
          <td :style="tdStyle" colspan="2">
            {{ placementApplication?.onlyChild }}
            <label v-for="(item, index) in TFOptions" :key="index" class="mr-2">
              {{ item.label }}
              <input :disabled="true" type="checkbox" :checked="'1' === item.value" />
            </label>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">户籍所在地</td>
          <td :style="tdStyle" colspan="4">{{ record?.additionalData?.registeredAddress }}</td>
          <td :style="tdStyle">是否单亲</td>
          <td :style="tdStyle" colspan="2">
            <label v-for="(item, index) in TFOptions" :key="index" class="mr-2">
              {{ item.label }}
              <input :disabled="true" type="checkbox" :checked="getValue(record?.onlyChild) === item.value" />
            </label>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">现居住地址</td>
          <td :style="tdStyle" colspan="4">{{ record?.additionalData?.currentAddress }}</td>
          <td :style="tdStyle">主要照顾者</td>
          <td :style="tdStyle" colspan="2">{{ record?.additionalData?.guardian }}</td>
        </tr>
        <tr>
          <td :style="tdStyle">残疾类别</td>
          <td :style="tdStyle" colspan="4">
            <div>
              <label v-for="(item, index) in disCategory" :key="index" class="mr-2">
                {{ item }}
                <input :disabled="true" type="checkbox" :checked="record?.additionalData?.disorders?.includes(item)" />
              </label>
            </div>
          </td>
          <td :style="tdStyle">残疾等级</td>
          <td :style="tdStyle" colspan="2">
            <div class="flex">
              <label v-for="(item, index) in disLevel" :key="index" class="mr-2">
                {{ item }}
                <input :disabled="true" type="checkbox" :checked="record.disabilityLevel === item" />
              </label>
            </div>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">残疾证号</td>
          <td :style="tdStyle" colspan="4">{{ record?.disabilityCertificateNo }}</td>
          <td :style="tdStyle">有无自伤史</td>
          <td :style="tdStyle" colspan="2">{{ record?.additionalData?.selfHarmHistory ? '是' : '否' }}</td>
        </tr>
        <tr>
          <td :style="tdStyle">是否患有疾病</td>
          <td :style="tdStyle" colspan="4">
            <div class="flex justify-start items-center space-x-2">
              <label v-for="(item, index) in diseaseTypes" :key="index">
                {{ item }}
                <input :disabled="true" type="checkbox" :checked="record?.additionalData?.disease?.includes(item)" />
              </label>
            </div>
            <div class="">
              {{ record?.additionalData?.otherDisease }}
            </div>
          </td>
          <td :style="tdStyle">过敏史</td>
          <td :style="tdStyle" colspan="2">
            <label v-for="(item, index) in YNOptions" :key="index" class="mr-2">
              {{ item.label }}
              <input :disabled="true" type="checkbox" :checked="getValue(record?.allergyHistory) === item.value" />
              <span v-if="'1' === item.value" class="ml-2">过敏源：</span>
              <br />
            </label>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">是否长期用药</td>
          <td :style="tdStyle" colspan="4">
            <label v-for="(item, index) in TFOptions" :key="index" class="mr-2">
              {{ item.label }}
              <input :disabled="true" type="checkbox" :checked="'1' === item.value" />
              <span v-if="'1' === item.value" class="ml-2">所用药物：{{ record?.additionalData?.medicine }}</span>
              <br />
            </label>
          </td>
          <td :style="tdStyle">用药副作用</td>
          <td :style="tdStyle" colspan="2">{{ record?.additionalData?.medicineSideEffect }}</td>
        </tr>
        <tr>
          <td :style="tdStyle">是否享受救助政策</td>
          <td :style="tdStyle" colspan="4">
            <label v-for="(item, index) in TFOptions" :key="index" class="mr-2">
              {{ item.label }}
              <input :disabled="true" type="checkbox" :checked="getValue(record?.enjoyAidPolicy) === item.value" />
              <span v-if="'1' === item.value" class="ml-2"
                >享受的政策：{{ record?.additionalData?.enjoyAidPolicyDescription }}</span
              >
              <br />
            </label>
          </td>
          <td :style="tdStyle">上下学方式</td>
          <td :style="tdStyle" colspan="2">
            <label v-for="(item, index) in gotoSchoolAndFromSchool" :key="index" class="mr-2">
              {{ item }}
              <input :disabled="true" type="checkbox" :checked="record?.additionalData?.commuteMethod === item" />
              <br />
            </label>
          </td>
        </tr>
        <tr>
          <td colspan="8" :style="tdStyle">
            <div class="text-xl flex justify-center font-bold"> 家庭成员情况（请填写长期共同居住的家庭成员）</div>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">关系</td>
          <td :style="tdStyle">姓名</td>
          <td :style="tdStyle" colspan="2">身份证号码</td>
          <td :style="tdStyle">手机号码</td>
          <td :style="tdStyle" colspan="2">工作单位</td>
          <td :style="tdStyle">文化程度</td>
        </tr>
        <tr v-for="item in record?.familyMembers" :key="item">
          <td :style="tdStyle">{{ item?.relationship }}</td>
          <td :style="tdStyle">{{ item?.name }}</td>
          <td :style="tdStyle" colspan="2">{{ item?.remark }}</td>
          <td :style="tdStyle">{{ item?.phone }}</td>
          <td :style="tdStyle" colspan="2">{{ item?.jobTitle }}</td>
          <td :style="tdStyle">{{ item?.education }}</td>
        </tr>
        <tr>
          <td :style="tdStyle"> 家长安置意向</td>
          <td :style="tdStyle" colspan="7">
            <div class="flex justify-start items-center space-x-4">
              <label v-for="(item, index) in resettlementType" :key="index">
                {{ item }}
                <input
                  :disabled="true"
                  type="checkbox"
                  :checked="record?.additionalData?.resettlementType?.includes(item)"
                />
              </label>
            </div>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle"> 教养方式</td>
          <td :style="tdStyle" colspan="7">
            <div class="flex justify-start items-center space-x-4">
              <label v-for="(item, index) in familyTeachingType" :key="index">
                {{ item }}
                <input
                  :disabled="true"
                  type="checkbox"
                  :checked="record?.additionalData?.familyTeachingType === item"
                />
              </label>
            </div>
          </td>
        </tr>
        <tr>
          <td colspan="8" :style="tdStyle">
            <div class="text-xl flex justify-center font-bold">社区环境</div>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">
            <div>社区环境</div>
          </td>
          <td colspan="8" :style="tdStyle">
            <div>{{ record?.additionalData?.activityDescription }}</div>
          </td>
        </tr>
        <tr>
          <td :style="tdStyle">
            <div>
              学生在社<br />
              区中活动 <br />情况描述
            </div>
          </td>
          <td colspan="8" :style="tdStyle">
            <div>{{ record?.additionalData?.communityEnvironment }}</div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!--量表评估-->
  <div
    v-for="item in assessmentDetails?.details"
    id="printArea"
    :key="item?.id"
    style="width: 210mm"
    class="rounded-lg m-auto p-2 mt-[40px]"
  >
    <div class="w-full flex justify-around space-x-8">
      <span>评估者：{{ item.cbName }}</span>
      <span>评估学生：{{ record?.name }}</span>
      <span>评估日期：{{ item.createdDate.split(' ')[0] }}</span>
      <span>得分：{{ sumTotalScore(item?.details) }}</span>
    </div>
    <table class="w-full border-collapse">
      <thead>
        <tr>
          <th :style="thStyle" class="text-xl font-bold" colspan="5">
            {{ assessmentDetails?.criteriaName?.[item.resultId] || '' }}
          </th>
        </tr>
        <tr>
          <th :style="thStyle">一级指标</th>
          <th :style="thStyle">二级指标</th>
          <th :style="thStyle">三级指标</th>
          <th :style="thStyle">评分标准</th>
          <th :style="thStyle">得分</th>
        </tr>
      </thead>
      <tbody>
        <template v-for="(row, index) in flattenData(item.details)" :key="index">
          <tr>
            <!-- 一级指标 -->
            <td v-if="row.level === 1" :style="tdStyle" :rowspan="row.level === 1 ? row.rowspan : 1">
              {{ row.name }}
            </td>

            <!-- 二级指标 -->
            <td v-if="row.level === 2" :style="tdStyle" :rowspan="row.level === 2 ? row.rowspan : 1">
              {{ row.name }}
            </td>

            <!-- 三级指标 -->
            <td v-if="row.level === 3" :style="tdStyle">
              {{ row.level === 3 ? row.name : `${row.name}--` }}
            </td>
            <!-- 标准 -->
            <td
              v-if="index === 2"
              :style="tdStyle"
              style="text-align: center"
              :rowspan="flattenData(item.details)?.length"
            >
              <span v-html="assessmentDetails?.scoringCriteria[item?.resultId].split(' ').join('<br />')" />
            </td>

            <!-- 得分 -->
            <td v-if="![1, 2].includes(row.level)" :style="tdStyle" style="text-align: center">
              {{ row?.score ? row?.score : '0' }}
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>

  <!--心理评估-->
  <div id="printArea" style="width: 210mm" class="rounded-lg m-auto p-2 mt-8">
    <ReportPage
      v-model:visible="psychologicalVisible"
      :assessment-data="psychologicalAssessment"
      style="page-break-inside: avoid"
      :print-able="false"
    />
  </div>

  <!--安置报告表单-->
  <div id="printArea" style="width: 210mm; page-break-inside: avoid" class="rounded-lg m-auto p-2 mt-8">
    <div class="flex justify-center items-center flex-col text-2xl mb-4 font-bold">
      <div>成都市武侯区残疾人教育专家委员会</div>
      <div>残疾儿童少年入学（转学）评估安置报告</div>
    </div>
    <table class="w-full border border-collapse">
      <tbody>
        <tr>
          <td :class="cls" rowspan="6">
            <div class="font-bold">残疾</div>
            <div class="font-bold">儿童</div>
            <div class="font-bold">少年</div>
            <div class="font-bold">基本</div>
            <div class="font-bold">信息</div>
          </td>
          <td :class="cls">姓名</td>
          <td :class="cls">{{ record?.name }}</td>
          <td :class="cls">性 别</td>
          <td :class="cls">{{ record?.gender }}</td>
          <td :class="cls">民 族</td>
          <td :class="cls">{{ record?.nation }}</td>
        </tr>
        <tr>
          <td :class="cls">出生日期</td>
          <td :class="cls">{{ record?.birthday }}</td>
          <td :class="cls">身份证号</td>
          <td :class="cls">{{ record?.idCardNo }}</td>
          <td :class="cls">残疾证号</td>
          <td :class="cls">{{ record?.disabilityCertificateNo }}</td>
        </tr>
        <tr>
          <td :class="cls">残疾等级</td>
          <td :class="cls">{{ record?.disabilityLevel }}</td>
          <td :class="cls">残疾类别</td>
          <td :class="cls">{{ record?.disorders }}</td>
          <td :class="cls">户 籍</td>
          <td :class="cls">{{ record?.additionalData?.census }}</td>
        </tr>
        <tr v-for="(item, index) in record?.familyMembers" :key="index">
          <td :class="cls">关系</td>
          <td :class="cls">{{ item.relationship }}</td>
          <td :class="cls">姓名</td>
          <td :class="cls">{{ item.name }}</td>
          <td :class="cls">联系电话</td>
          <td :class="cls">{{ item.phone }}</td>
        </tr>
        <tr>
          <td :class="cls" colspan="3">家庭住址</td>
          <td :class="cls" colspan="3">{{ placementAssessment?.additionalData?.adresss }}</td>
        </tr>
        <tr>
          <td :class="cls" colspan="2">家长安置意向</td>
          <td :class="cls" colspan="5">
            <div class="flex justify-start flex-col px-2">
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.intention === '1'"
                />
                普通学校
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.intention === '2'"
                />
                特殊教育学校
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.intention === '3'"
                />
                送教上门
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.intention === '4'"
                />
                其他<span style="text-decoration: underline; margin-left: 10px">
                  {{ placementAssessment?.additionalData?.intentionOthers }}
                </span>
              </label>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="7">评估情况</td>
        </tr>
        <tr>
          <td :class="cls" colspan="2">评估领域</td>
          <td :class="cls" colspan="5">评估结果</td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 学科能力</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('学科能力') }}</div>
              <div>现状描述：{{ getDescription('学科能力') }}</div>
              <div>建议：{{ getSuggestion('学科能力') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 生活自理</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('生活自理') }}</div>
              <div>现状描述：{{ getDescription('生活自理') }}</div>
              <div>建议：{{ getSuggestion('生活自理') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 感官知觉</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('感官知觉') }}</div>
              <div>现状描述：{{ getDescription('感官知觉') }}</div>
              <div>建议：{{ getSuggestion('感官知觉') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 感官知觉</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('感官知觉') }}</div>
              <div>现状描述：{{ getDescription('感官知觉') }}</div>
              <div>建议：{{ getSuggestion('感官知觉') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 精细动作</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('精细动作') }}</div>
              <div>现状描述：{{ getDescription('精细动作') }}</div>
              <div>建议：{{ getSuggestion('精细动作') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 语言沟通</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('语言沟通') }}</div>
              <div>现状描述：{{ getDescription('语言沟通') }}</div>
              <div>建议：{{ getSuggestion('语言沟通') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 心理健康</td>
          <td :class="cls" colspan="5">
            <div style="text-align: start">
              <div>得分：{{ getScore('心理健康') }}</div>
              <div>现状描述：{{ getDescription('心理健康') }}</div>
              <div>建议：{{ getSuggestion('心理健康') }}</div>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 安置建议</td>
          <td :class="cls" colspan="5">
            <div class="flex justify-start flex-col">
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.placementSuggestion === '1'"
                />
                普通学校
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.placementSuggestion === '2'"
                />
                特殊教育学校
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.placementSuggestion === '3'"
                />
                送教上门
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.placementSuggestion === '4'"
                />
                其他<span style="text-decoration: underline; margin-left: 10px">
                  {{ placementAssessment?.additionalData?.placementSuggestionOthers }}
                </span>
              </label>
            </div>
          </td>
        </tr>
        <tr>
          <td :class="cls" colspan="2"> 家长意见</td>
          <td :class="cls" colspan="5">
            <div class="flex justify-start flex-col">
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.parentsSuggestion === '1'"
                />
                知晓并同意以上安置意见
              </label>
              <label class="flex items-center">
                <input
                  :disabled="true"
                  type="checkbox"
                  class="mr-2"
                  :checked="placementAssessment?.additionalData?.parentsSuggestion === '2'"
                />
                知晓但不同意以上安置意见
              </label>
              <label class="flex items-center"> 家长签名: _______________ 日期：_______________ </label>
            </div>
          </td>
        </tr>
        <tr style="page-break-inside: avoid">
          <td :class="cls" colspan="2">
            <div>残疾人教育专家</div>
            <div>委员会评估组专</div>
            <div>家签名</div>
          </td>
          <td :class="cls" colspan="5">
            <div class="relative h-[250px]">
              <div class="absolute right-[20px] bottom-[60px]">武侯区残疾人教育专家委员会（盖章）</div>
              <div class="absolute right-[28px] bottom-[20px]">
                年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日
              </div>
            </div>
          </td>
        </tr>
        <tr style="page-break-inside: avoid">
          <td :class="cls" colspan="2">
            <div>教育主管行</div>
            <div>政部门意见</div>
          </td>
          <td :class="cls" colspan="5">
            <div class="relative h-[250px]">
              <div class="absolute right-[20px] bottom-[60px]">武侯区残疾人教育专家委员会（盖章）</div>
              <div class="absolute right-[28px] bottom-[20px]">
                年&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;日
              </div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped lang="scss"></style>
