<script setup lang="ts">
  import { computed, inject, onMounted, provide, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import AssessmentAnalysisView from '@repo/components/assessment/assessmentAnalysisView.vue';
  import CustomizeComponent from '@repo/infrastructure/customizeComponent/customizeComponent.vue';
  import RecordDetail from '@repo/ui/components/record-detail/index.vue';
  import DevStudentView from '@repo/components/development/dev-student-view.vue';
  import useCommonStore from '@repo/infrastructure/utils/store';
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';
  import { VuePrintNext } from 'vue-print-next';
  import ReportPage from '@repo/components/specialEduCommittee/ReportPage.vue';
  import { getOrgNature } from '@repo/components/utils/utils';

  // import resettlementStudentPrintViewDemo from '@/views/manage/components/student/resettlementStudentPrintViewDemo.vue';

  const props = defineProps({
    row: {
      type: Object,
      required: true,
    },
    schema: {
      type: Object,
      required: true,
    },
  });

  const teacherStore = useCommonStore({
    api: '/org/companyUser/allTeachers',
  });
  const teachersMap = ref({});

  const activeTab = ref('basic-info');
  const assessmentList = ref<any[]>([]);
  const printVisible = ref(false);
  const printViewRef = ref(null);
  const printRef = ref(null);
  const title = computed(() => {
    return props.row?.name;
  });
  const currentCriterion = ref(null);

  // use the api like this has some problem
  const handleBeforeOpen = async () => {
    activeTab.value = 'basic-info';
    currentCriterion.value = null;
    assessmentList.value = [];

    const { data } = await request({
      url: `/evaluation/customCriterionResult/findByStudentIdAndNature`,
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      params: {
        studentId: props.row.id,
        nature: getOrgNature(),
        pageSize: 999,
      },
    });
    assessmentList.value = data;
  };

  onMounted(async () => {
    teachersMap.value = await teacherStore.getMap();
  });
  provide('VuePrintNext', VuePrintNext);
  provide('RecordDetail', RecordDetail);
  provide('AvatarDisplay', AvatarDisplay);

  provide('ReportPage', ReportPage);
  provide('request', request);
  provide('PROJECT_URLS', PROJECT_URLS);
  provide('getOrgNature', getOrgNature);

  const handlePrint = () => {
    printRef.value.handlePrint();
    // printViewRef.value?.handlePrint();
  };
</script>

<template>
  <a-modal v-bind="$attrs" :title="title" fullscreen hide-cancel hide-title @before-open="handleBeforeOpen">
    <a-tabs v-if="$attrs.visible" v-model:active-key="activeTab">
      <template #extra>
        <div>
          <a-button size="mini" @click="printVisible = true">
            <template #icon>
              <icon-printer />
            </template>
            打印预览
          </a-button>
        </div>
      </template>
      <a-tab-pane key="basic-info" title="基本信息">
        <customize-component
          ref="customizeRef"
          :teachers-map="teachersMap"
          :raw="row"
          :model-value="row"
          :schema="schema"
          module="Student"
          page="ResettlementApply"
        >
          <!--<record-detail v-if="schema && row" ref="recordDetailRef" :raw="row" :schema="schema" />-->
          <dev-student-view :raw="row" :teachers-map="teachersMap" :schema="schema" />
        </customize-component>
      </a-tab-pane>
      <!--安置申请报告View-->
      <a-tab-pane v-if="row?.entranceType === 'ResettlementApplication'" title="入学安置申请">
        <customize-component
          ref="customizeRef"
          :raw="row"
          :model-value="row"
          :schema="schema"
          module="ResettlementApplication"
          page="View"
        >
          <!--<ResettlementApplicationViewDemo :schema="schema" :record="row" />-->
        </customize-component>
      </a-tab-pane>
      <a-tab-pane key="assessment" title="评估记录">
        <div v-if="assessmentList.length === 0">
          <a-empty description="暂无评估记录" />
        </div>
        <div v-else class="flex gap-2 w-full">
          <div class="border-r border-r-slate-200 px-2 min-w-64">
            <div
              v-for="item in assessmentList"
              :key="item.id"
              class="p-2 border-b border-b-slate-200 cursor-pointer"
              :class="{ 'bg-slate-100': currentCriterion?.id === item.id }"
              @click="() => (currentCriterion = item)"
            >
              <div class="p-2">{{ item.criterionName }}</div>
              <div class="text-sm text-gray-400 px-2"> 评估时间：{{ item.evaluationDate }} </div>
              <div class="text-sm text-gray-400 px-2"> 评估专家：{{ item.evaluator?.name }} </div>
            </div>
          </div>
          <div v-if="!currentCriterion" class="flex-1">
            <a-empty description="请先在左侧选择评估记录" />
          </div>
          <assessment-analysis-view
            v-else
            class="w-full"
            :show-result-filter="false"
            :result-id="currentCriterion?.id"
            :current-times="currentCriterion?.times"
          />
        </div>
      </a-tab-pane>
    </a-tabs>
    <a-modal v-model:visible="printVisible" fullscreen hide-cancel ok-text="关闭">
      <template #title>
        <div class="grid grid-cols-3 items-center w-full">
          <div class="col-span-1"></div>
          <span class="col-span-1 text-center">安置学生信息打印</span>
          <a-button class="col-span-1 ml-auto mr-4" size="mini" @click="handlePrint">
            <template #icon>
              <icon-printer />
            </template>
            打印
          </a-button>
        </div>
      </template>
      <!--自定义组件-->
      <!--<resettlementStudentPrintViewDemo v-if="printVisible" ref="printViewRef" :record="row" :schema="schema" />-->
      <customize-component
        v-if="printVisible"
        ref="printRef"
        :model-value="row"
        :schema="schema"
        module="SpecialCommitteeManagerClient"
        page="Print"
      >
        <template #default>
          <a-empty />
        </template>
      </customize-component>
    </a-modal>
  </a-modal>
</template>

<style scoped lang="scss"></style>
