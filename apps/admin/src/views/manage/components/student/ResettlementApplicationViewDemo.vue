<!--
@todo Deletion is needed when customize_component is configured
-->
<script setup lang="ts">
  import { PropType, inject } from 'vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object as PropType<any>,
    },
    schema: {
      type: Object as PropType<any>,
    },
  });
  const RecordDetail = inject('RecordDetail');

  const switch2Chinese = (val) => {
    switch (val) {
      case '1':
        return '壹级';
      case '2':
        return '贰级';
      case '3':
        return '叁级';
      case '4':
        return '肆级';
      default:
        return val;
    }
  };

  const getParentPlacement = (val) => {
    const categories = ['普通学校随班', '普通学校特教班', '特教学校', '送教上门'];
    const result = [];
    val?.forEach((item: string) => {
      result.push(categories[Number(item) - 1]);
    });
    return result.join(', ');
  };
</script>

<template>
  <div class="w-full flex flex-col space-y-4">
    <RecordDetail ref="recordDetailRef" :raw="record" :schema="schema" />
    <a-descriptions :column="4" bordered>
      <a-descriptions-item :span="1" label="主要照顾者">
        {{ record.student?.guardian }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="是否单亲">
        {{ record.additionalData?.singleParent ? '是' : '否' }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="否是独生子女">
        {{ record.additionalData?.onlyChild ? '是' : '否' }}
      </a-descriptions-item>

      <a-descriptions-item :span="1" label="残疾类别">
        {{ record.additionalData?.disorders.join(' ,') }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="残疾等级">
        {{ switch2Chinese(record.additionalData?.disabilityLevel) }}
      </a-descriptions-item>
      <a-descriptions-item :span="2" label="有无自伤史">
        {{ record.additionalData?.selfHarmHistory ? '是' : '否' }}
      </a-descriptions-item>

      <a-descriptions-item :span="1" label="是否患有疾病">
        <div>{{ record.additionalData?.disease?.join(', ') }}</div>
        <div> {{ record.additionalData?.otherDisease }} </div>
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="过敏史">
        {{ record.additionalData?.allergyHistory ? '有' : '无' }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="是否长期用药">
        {{ record.additionalData?.medicineHistory ? '是' : '否' }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="用药副作用">
        {{ record.additionalData?.medicineSideEffect }}
      </a-descriptions-item>

      <a-descriptions-item :span="1" label="是否享受救助政策">
        {{ record.additionalData?.enjoyAidPolicy ? '是' : '否' }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="上下学方式">
        {{ record.additionalData?.commuteMethod }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="家长安置意向">
        {{ getParentPlacement(record.additionalData?.placementOpinion) }}
      </a-descriptions-item>
      <a-descriptions-item :span="1" label="教养方式">
        {{ record.additionalData?.familyTeachingType }}
      </a-descriptions-item>

      <a-descriptions-item :span="4" label="社区环境描述">
        {{ record.additionalData?.communityEnvironment }}
      </a-descriptions-item>
      <a-descriptions-item :span="4" label="学生在社区中活动情况描述">
        {{ record.additionalData?.activityDescription }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped lang="scss"></style>
