<script setup lang="ts">
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { useTeacherStore } from '@repo/infrastructure/store';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import TeachingArchiveDetail from '@repo/components/course/teachingArchive/teachingArchiveDetail.vue';
  import { getChapter } from '@repo/infrastructure/openapi/course';

  const props = defineProps({
    company: {
      type: Object,
    },
    period: {
      type: String,
    },
    allGrades: {
      type: Array,
    },
    allGradeClasses: {
      type: Array,
    },
    viewDateRange: {
      type: Array,
    },
    /* lessonCount: {
    type: Object,
  }, */
    currentTask: {
      type: Object,
    },
  });

  const gradeClassesTree = ref<any[]>([]);
  const gradeClassesMap = ref<any>({});
  const ready = ref(false);
  const selectedValue = ref();
  const currentGradeClass = ref<any>(null);
  const gradeClasses = ref<any[]>([]);

  const currentTimetable = ref<any>(null);
  const currentPeriod = ref<string>('');
  const lessonCount = ref({
    morning: null,
    afternoon: null,
  });

  const grades = ref<any[]>([]);

  const handleData = (val?: number) => {
    if (!val && props.company) {
      grades.value = props.allGrades.filter((grade) => grade.school.id === props.company);
      const gradeIds = grades.value.map((grade) => grade.id);
      gradeClasses.value = props.allGradeClasses.filter((gradeClass) => gradeIds.includes(gradeClass.grade.id));
      if (!props.company) {
        currentPeriod.value = '';
        currentTimetable.value = null;
      }
    }

    gradeClassesMap.value = gradeClasses.value.reduce((acc: any, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});

    const classesMap = gradeClasses.value.reduce((acc: any, item: any) => {
      if (!acc[item.grade?.id]) {
        acc[item.grade?.id] = [];
      }
      acc[item.grade?.id].push({
        id: item.id,
        key: `grade-class-${item.id}`,
        name: item.name,
        title: item.name,
        type: 'gradeClass',
        gradeId: item.grade?.id,
        children: [],
      });
      return acc;
    }, {});

    gradeClassesTree.value = grades.value.map((grade: any) => {
      return {
        id: grade.id,
        key: `grade-${grade.id}`,
        title: grade.name,
        children: classesMap?.[grade.id] || [],
        type: 'grade',
      };
    });
  };

  const daysInRange = ref(null);
  const dateRange = ref<string[]>([]);
  const currentPage = ref(0);

  const checkedKeys = ref();
  const checkStrictly = ref(false);
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  const generateDateRange = (startDate: Date, endDate: Date): string[] => {
    const dates: string[] = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }
    return dates;
  };

  const handleDateRange = () => {
    if (!props.viewDateRange) {
      // 如果没有传入 viewDateRange，生成默认时间范围（今天往后七天）
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + 6); // 往后七天
      dateRange.value = generateDateRange(startDate, endDate);
    } else {
      // 如果有传入 viewDateRange，使用传入的时间范围
      const [startDate, endDate] = props.viewDateRange;
      if (startDate && endDate) {
        dateRange.value = generateDateRange(new Date(startDate), new Date(endDate));
      }
    }
  };

  const paginatedDateRange = computed(() => {
    const start = currentPage.value * 7;
    const end = start + 7;
    const slicedDates = dateRange.value.slice(start, end);

    while (slicedDates.length < 7) {
      slicedDates.push('');
    }
    return slicedDates;
  });

  const nextPage = () => {
    if ((currentPage.value + 1) * 7 < dateRange.value.length) {
      currentPage.value += 1;
    }
  };

  const prevPage = () => {
    if (currentPage.value > 0) {
      currentPage.value -= 1;
    }
  };

  const currentGradeAssignment = ref([]); // 当前班级的课表详情
  const handleGradeClassChange = (val: any) => {
    currentGradeClass.value = val;
    if (val.length > 0) {
      const arr = val[0].split('-');
      if (!(arr.length === 3)) return;
      currentGradeAssignment.value = props.currentTask?.assignments.filter(
        (assignment) => assignment.gradeClassId === Number(arr[2]),
      );
      props.currentTask?.gradeConditions.forEach((condition) => {
        if (condition?.gradeId === currentGradeAssignment.value[0]?.gradeId) {
          lessonCount.value.morning = condition.morningCount;
          lessonCount.value.afternoon = condition.afternoonCount;
        }
      });
    }
  };

  const classInfo = (x: number, y: number) => {
    return currentGradeAssignment.value?.find((item) => {
      return item.coordinate?.x === x && item.coordinate?.y === y;
    });
  };

  const schoolCourseStore = useSchoolCourseStore();

  const lessonsMap = ref();
  const getLessons = async () => {
    lessonsMap.value = await schoolCourseStore.getSchoolCoursesMap();
  };
  const teachers = ref<any[]>([]);
  const getTeachers = async () => {
    const teacherStore = useTeacherStore();
    teachers.value = await teacherStore.getTeachersList();
  };

  const teachersMap = computed(() => {
    return teachers.value.reduce((acc, teacher) => {
      acc[teacher.id] = teacher;
      return acc;
    }, {});
  });

  // 当前班级教师ids
  const teacherIds = computed(() => {
    const uniqueIdsSet = new Set<number>();
    currentGradeAssignment.value.forEach((item) => {
      const teacherId = item?.teacherId;
      if (teacherId !== undefined && teacherId !== null) {
        uniqueIdsSet.add(teacherId);
      }
    });
    return Array.from(uniqueIdsSet);
  });

  const chapterMap = ref(null);
  const currentChapterInfo = ref();
  const loadTeachingLesson = async (range?: string[]) => {
    const { data: res } = await request('/course/chapter/getChapterListBySchedule', {
      method: 'put',
      params: {
        cbIds: teacherIds.value,
        classDate: range || props.viewDateRange,
        period: props.period,
      },
    });
    chapterMap.value = res.list;
  };

  const lessonPlanVisible = ref(false);
  const currentClassInfo = ref();
  const currentDate = ref<Date>();

  const formatDate = (date: Date): string => {
    date = new Date(date);
    const year = date?.getFullYear();
    const month = String(date?.getMonth() || `${1}`).padStart(2, '0');
    const day = String(date?.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const findMatchingChapter = (date: Date, classIndex: number, isMorning: boolean) => {
    const currentDateStr = formatDate(date);
    let matchingChapter = null;

    chapterMap.value?.forEach((item) => {
      const itemDateStr = formatDate(new Date(item.classDate));
      if (itemDateStr === currentDateStr) {
        item.chapters?.forEach((chapter) => {
          if (chapter.submitted)
            if (isMorning) {
              if (chapter.lesson[0]?.includes(classIndex)) matchingChapter = chapter;
            } else if (chapter.lesson[1]?.includes(classIndex)) {
              matchingChapter = chapter;
            }
        });
      }
    });
    return matchingChapter;
  };

  const handleViewTeachingPlan = (date: Date, info: any, isMorning: boolean) => {
    if (!info?.teacherId) {
      Message.info('当前节次没有课程安排');
      return;
    }
    const matchingChapter = findMatchingChapter(new Date(date), info.classIndex, isMorning);
    if (!matchingChapter) {
      Message.info('暂无可查看的教案');
      return;
    }
    currentDate.value = new Date(date);
    currentClassInfo.value = info;
    currentChapterInfo.value = findMatchingChapter(currentDate.value, info.classIndex, isMorning);
    lessonPlanVisible.value = true;
  };

  const getBgStyle = (index: number, dayIndex: number, date: string, info: any) => {
    const styles = [];
    if (
      index === lessonCount.value.morning + lessonCount.value.afternoon - 1 &&
      dayIndex === paginatedDateRange.value.length - 1
    ) {
      styles.push('rounded-br-lg');
    }
    // 背景颜色样式
    if (info?.teacherId) {
      const isMorning = index < lessonCount.value.morning;
      const matchingChapter = findMatchingChapter(new Date(date), info.classIndex, isMorning);

      if (matchingChapter) {
        styles.push('bg-green-50'); // 匹配且有教师
      } else {
        styles.push('bg-blue-50'); // 未匹配但有教师
      }
    } else {
      styles.push('bg-red-50'); // 未分配教师
    }

    return styles.join(' ');
  };

  const loading = ref(false);
  const course = ref();
  const loadChapterDetails = async () => {
    loading.value = true;
    try {
      const { data: courseData } = await request(`/course/course/${currentChapterInfo.value.courseId}`);
      course.value = courseData;
      const { data } = await getChapter({
        id: currentChapterInfo.value.id,
      });
      currentChapterInfo.value = data;
    } finally {
      loading.value = false;
    }
  };
  const emits = defineEmits(['updateDateRange']);
  onMounted(async () => {
    handleDateRange();
    emits('updateDateRange', [dateRange.value[0], dateRange.value[dateRange.value.length - 1]]);
    currentPage.value = 0;
    handleData();
    await Promise.all([getLessons(), getTeachers(), loadTeachingLesson()]);
    ready.value = true;
    currentGradeClass.value = [`grade-class-${gradeClassesTree.value[0]?.children[0]?.id}`];
    handleGradeClassChange(currentGradeClass.value);
  });

  watch(
    () => props.viewDateRange,
    async (newRange) => {
      if (newRange) {
        handleDateRange();
        currentPage.value = 0; // 重置页码
        await loadTeachingLesson(); // 获取新的教案
      }
    },
    { deep: true },
  );

  watch(
    () => props.company,
    async (val) => {
      grades.value = props.allGrades.filter((grade) => grade.school.id === val);
      const gradeIds = grades.value.map((grade) => grade.id);
      gradeClasses.value = props.allGradeClasses.filter((gradeClass) => gradeIds.includes(gradeClass.grade.id));
      if (!val) {
        currentPeriod.value = '';
        currentTimetable.value = null;
      }
      handleData(val);
    },
    { deep: true },
  );
  watch(
    () => props.period,
    async (val) => {
      currentGradeClass.value = null;
      selectedValue.value = [];
    },
    { deep: true },
  );
</script>

<template>
  <div v-if="ready">
    <a-split v-model:size="splitSize" class="mt-2 split-container" max="300px">
      <template #first>
        <a-tree
          v-if="gradeClassesTree.length > 0"
          v-model:selected-keys="selectedValue"
          :data="gradeClassesTree"
          show-line
          :default-selected-keys="currentGradeClass"
          :selectable="true"
          @select="handleGradeClassChange"
        >
          <template #title="node">
            {{ node.name || node.title }}
          </template>
        </a-tree>
      </template>
      <template #second>
        <div v-if="currentGradeClass" class="container mx-auto p-4 rounded flex justify-between items-center">
          <icon-left
            class="font-bold text-lg mr-4 size-10 text-gray-400 hover:text-blue-500 cursor-pointer"
            @click="prevPage"
          />
          <table class="table-fixed w-full border-collapse border text-center rounded-lg overflow-hidden">
            <thead>
              <tr class="bg-gray-200">
                <th class="border border-gray-300 w-1/6 py-2 rounded-tl-lg">
                  <div class="px-4 items-center justify-center h-10 flex">
                    <span>时间{{ daysInRange }}</span>
                  </div>
                </th>
                <th
                  v-for="(date, index) in paginatedDateRange"
                  :key="index"
                  class="border border-gray-300 w-1/6"
                  :class="index === paginatedDateRange.length - 1 ? 'rounded-tr-lg' : ''"
                >
                  <div class="flex px-4 items-center h-10" :class="date ? 'justify-between' : 'justify-center'">
                    <span v-if="date" class="text-xs text-gray-400">{{ date }}</span>
                    <span v-else class="text-xs text-gray-400">无数据</span>
                    <span v-if="date" class="text-black">
                      {{ weekdays[new Date(date).getDay()] }}
                    </span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody class="bg-gray-50">
              <tr v-for="(timeSlot, index) in lessonCount.morning + lessonCount.afternoon" :key="index">
                <td
                  class="border border-gray-300 px-2 py-2 font-semibold h-20"
                  :class="index === lessonCount.morning + lessonCount.afternoon - 1 ? 'rounded-bl-lg' : ''"
                >
                  {{
                    index < lessonCount.morning ? `上午第${index + 1}节` : `下午第${index - lessonCount.morning + 1}节`
                  }}
                </td>
                <td
                  v-for="(date, dayIndex) in paginatedDateRange"
                  :key="dayIndex"
                  class="border border-gray-300 px-4 py-2 h-20 cursor-pointer hover:bg-sky-100"
                  :class="getBgStyle(index, dayIndex, date, classInfo(new Date(date).getDay() - 1, index), lessonCount)"
                  @click="
                    handleViewTeachingPlan(
                      date,
                      classInfo(new Date(date).getDay() - 1, index),
                      index < lessonCount.morning,
                    )
                  "
                >
                  <div v-if="date" class="w-full h-full">
                    <div>
                      {{ teachersMap[classInfo(new Date(date).getDay() - 1, index)?.teacherId]?.name }}
                    </div>
                    <div class="font-bold">
                      <span>{{ lessonsMap[classInfo(new Date(date).getDay() - 1, index)?.lessonId]?.name || '' }}</span>
                    </div>
                  </div>
                  <span v-else />
                </td>
              </tr>
            </tbody>
          </table>
          <icon-right
            class="font-bold text-lg ml-4 size-10 text-gray-400 hover:text-blue-500 cursor-pointer"
            @click="nextPage"
          />
        </div>
        <a-empty v-else :description="currentTask ? '请先在左侧选择章节' : '暂无排课'" />
      </template>
    </a-split>
  </div>
  <a-modal v-model:visible="lessonPlanVisible" fullscreen cancel-text="返回" @before-open="loadChapterDetails">
    <template #title>
      <div class="flex space-x-2 items-center">
        <span>
          {{ teachersMap?.[currentClassInfo?.teacherId]?.name }}
        </span>
        <span class="text-sm text-gray-500">
          {{ formatDate(currentDate) }}
        </span>
        <a-tag size="mini" color="green">{{ weekdays[currentDate?.getDay()] }}</a-tag>
      </div>
    </template>
    <a-spin v-if="lessonsMap" class="w-full" :loading="loading">
      <a-empty v-if="!currentChapterInfo?.id" />
      <div v-else class="flex-1 bg-slate-400 py-6 paper-wrapper shadow-inner p-4">
        <div :id="printAreaId" class="bg-white py-4 px-5 mx-auto page shadow-xl">
          <a-descriptions v-if="course?.id" title="课程信息" :column="4">
            <a-descriptions-item label="上课教师">
              {{ `${teachersMap?.[currentClassInfo?.teacherId]?.name}` }}
            </a-descriptions-item>
            <a-descriptions-item label="原创教师">
              {{ course.createdBy.name }}
            </a-descriptions-item>
            <a-descriptions-item label="科目">{{ lessonsMap[course.category]?.name }}</a-descriptions-item>
            <a-descriptions-item label="年级">{{ course.grade }} {{ course.period }}</a-descriptions-item>
            <a-descriptions-item label="教材版本">{{ course.description }}</a-descriptions-item>
            <a-descriptions-item label="上课时间">{{ currentChapter?.classTime || '-' }}</a-descriptions-item>
            <a-descriptions-item v-if="currentChapter?.content?.lessonPrepareAttachments?.length" label="课件">
              <attachments-preview-display :raw="currentChapter?.content?.lessonPrepareAttachments" />
            </a-descriptions-item>
            <a-descriptions-item label="引用教材说明">{{ currentChapter?.description || '-' }}</a-descriptions-item>
          </a-descriptions>
          <a-divider :margin="10" />
          <a-spin class="w-full p-2">
            <teaching-archive-detail
              v-if="currentChapterInfo && currentChapterInfo.content"
              :chapter="currentChapterInfo"
              :show-assessment="false"
              annotation-module=""
              current-directory=""
            />
          </a-spin>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<style scoped lang="scss">
  .split-container {
    height: calc(100vh - 145px);
  }

  .table-auto {
    width: 100%;
  }
</style>
