<script setup lang="ts">
  import QuestionnaireRecordList from '@/views/manage/components/questionnaire/questionnaireRecordList.vue';
  import { computed } from 'vue';

  const props = defineProps({
    formId: {
      type: Number,
      required: true,
    },
  });

  const form = computed(() => {
    return {
      id: props.formId,
    };
  });
</script>

<template>
  <a-modal v-bind="$attrs" :width="860" title="答卷列表">
    <questionnaire-record-list v-if="$attrs.visible && form.id" :form="form" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
