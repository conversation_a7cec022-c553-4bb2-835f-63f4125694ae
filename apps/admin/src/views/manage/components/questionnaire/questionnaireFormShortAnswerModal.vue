<script setup lang="ts">
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    formId: {
      type: Number,
      required: true,
    },
    questionId: {
      type: Number,
      required: true,
    },
  });

  const records = ref<any[]>([]);

  const handleOpen = async () => {
    const { data } = await request('/questionnaire/record', {
      baseURL: PROJECT_URLS.GO_PROJECT_API,
      params: {
        pageSize: 999,
        submitted: true,
        formId: props.formId,
      },
    });

    records.value = data.list
      .map((item: any) => {
        return {
          id: item.id,
          user: item.createdBy,
          content: item.answers[props.questionId],
        };
      })
      ?.filter((item) => item.content?.trim());
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="简答结果查看" :render-to-body="false" @open="handleOpen">
    <div v-for="(r, idx) in records" :key="idx">
      <div class="flex items-start gap-4">
        <div class="w-24 text-right"> {{ r.user?.name || '匿名用户' }}： </div>
        <div class="flex-1">
          {{ r.content }}
        </div>
      </div>
      <a-divider :margin="8" />
    </div>
  </a-modal>
</template>

<style scoped lang="scss"></style>
