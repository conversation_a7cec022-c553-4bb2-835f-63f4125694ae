<script setup lang="ts">
  import { computed, ref, watch, PropType } from 'vue';
  import { cloneDeep } from 'lodash';
  import { useWindowSize } from '@vueuse/core';
  import { QuestionEdit } from '@repo/components/question-library';
  import QuestionLibrary from '@repo/components/quesionLibrary/questionLibrary.vue';
  import {
    deleteQuestionnaireFolder,
    deleteQuestionnaire,
    getQuestionnaireList,
    updateFolderName,
    createNewQuestionnaireFolder,
    saveQuestionnaire,
    getQuestionnaire,
  } from '@repo/infrastructure/openapi/questionnaireController';
  import QuestionnaireFormDesignPreview from '@repo/components/questionnaireForm/questionnaireFormDesignPreview.vue';
  import { request } from '@repo/infrastructure/request';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
    checkedItems: {
      type: Array,
      default: () => [],
    },
    record: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  });

  const catalogType = computed(() => {
    return props.record?.scene === 'investigation' ? 1 : 2;
  });
  const { width } = useWindowSize();
  const saving = ref(false);
  const previewVisible = ref(true);
  const questionEditVisible = ref(false);
  const currentQuestion = ref<any>(null);
  const selectedQuestionIds = ref<number[]>([]);
  const questionConfig = ref<any>({});

  const splitSize = ref(width.value - 450);
  const emit = defineEmits(['update:form', 'refresh']);
  const formData = computed({
    get: () => cloneDeep(props.form),
    set: (value) => emit('update:form', value),
  });

  const handleOk = async () => {
    saving.value = true;
    try {
      const saveFormData = cloneDeep(formData.value);
      saveFormData.questionIds = selectedQuestionIds.value;
      saveFormData.questionConfig = questionConfig.value;

      await request(`/questionnaire/form/${formData.value.id}`, {
        method: 'PUT',
        data: saveFormData,
      });

      emit('refresh');

      return true;
    } catch (e) {
      return false;
    } finally {
      saving.value = false;
    }
  };

  const handleShowQuestionEdit = (data: any) => {
    currentQuestion.value = data;
    questionEditVisible.value = true;
  };

  const handleOpen = () => {
    selectedQuestionIds.value = formData.value.questionIds || [];
    const configs = formData.value.questionConfig || {};

    selectedQuestionIds.value?.forEach((id) => {
      configs[id] = configs[id] || {
        required: false,
      };
    });

    questionConfig.value = configs;
  };

  const handleAddSelected = (id: number) => {
    if (!selectedQuestionIds.value.includes(id)) {
      questionConfig.value[id] = {
        required: true,
      };
      selectedQuestionIds.value.push(id);
    }
  };

  const handleRemoveSelected = (id: number) => {
    selectedQuestionIds.value = selectedQuestionIds.value.filter((item) => item !== id);
  };
  const manageType = computed(() => {
    return props.record?.scene;
  });

  watch(
    () => previewVisible.value,
    (value) => {
      if (value) {
        splitSize.value = width.value - 450;
      } else {
        splitSize.value = width.value - 50;
      }
    },
  );
</script>

<template>
  <a-modal v-bind="$attrs" fullscreen title="试题管理" :on-before-ok="handleOk" :ok-loading="saving" @open="handleOpen">
    <template #title>
      <div class="flex justify-between w-full">
        <a-space>
          <div>{{ props.record?.scene === 'investigation' ? '问卷管理' : '试题管理' }}</div>
          <div v-if="$attrs.visible && form"> - {{ form.name }}</div>
        </a-space>
        <a-space class="mr-10">
          <a-switch v-model="previewVisible" type="round" />
          <small>预览</small>
        </a-space>
      </div>
    </template>
    <a-split v-if="formData?.id && $attrs.visible" v-model:size="splitSize" class="split-wrapper">
      <template #first>
        <question-library
          request-api="/questionnaire/folder"
          :on-delete-catalog="deleteQuestionnaireFolder"
          :on-delete-question="deleteQuestionnaire"
          :on-update-catalog-name="updateFolderName"
          :on-get-question-list="getQuestionnaireList"
          :on-create-new-catalog="createNewQuestionnaireFolder"
          :catalog-type="catalogType"
          :record="record"
          @go-edit-question="handleShowQuestionEdit"
        >
          <template #prepend-actions="{ record }">
            <div v-if="record.type === 'file'">
              <a-button
                v-if="selectedQuestionIds.indexOf(record.id) >= 0"
                size="mini"
                type="text"
                status="success"
                @click="() => handleRemoveSelected(record.id)"
              >
                <template #icon>
                  <IconCheck />
                </template>
                已选
              </a-button>
              <a-button v-else size="mini" type="text" @click="() => handleAddSelected(record.id)">
                <template #icon>
                  <IconFullscreen />
                </template>
                选择
              </a-button>
            </div>
          </template>
        </question-library>
      </template>
      <template v-if="previewVisible" #second>
        <a-card title="预览" :bordered="false" size="small">
          <a-empty v-if="!selectedQuestionIds?.length">在左侧选择试题</a-empty>
          <questionnaire-form-design-preview
            v-else
            v-model:question-ids="selectedQuestionIds"
            v-model:question-config="questionConfig"
            :form="formData"
          />
        </a-card>
      </template>
    </a-split>

    <a-modal
      v-model:visible="questionEditVisible"
      fullscreen
      title="试题编辑"
      hide-cancel
      ok-text="关闭"
      @ok="questionEditVisible = false"
    >
      <question-edit
        v-if="questionEditVisible"
        :edit-id="currentQuestion?.id"
        :handle-get-question="getQuestionnaire"
        :handle-save-question="saveQuestionnaire"
        :manage-type="manageType"
        @go-back="questionEditVisible = false"
      />
    </a-modal>
  </a-modal>
</template>

<style scoped lang="scss">
  .split-wrapper {
    height: calc(100vh - 160px);
  }
</style>
