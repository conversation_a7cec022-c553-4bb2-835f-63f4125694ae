<script setup lang="ts">
  import { CrudTable } from '@repo/ui/components/table';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { ref } from 'vue';
  import QuestionnaireRecordDetailModal from '@/views/manage/components/questionnaire/questionnaireRecordDetailModal.vue';
  import { questionnaireRecordReject } from '@repo/infrastructure/openapi/questionnaireRecordController';
  import { Modal } from '@arco-design/web-vue';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
  });

  const schema = SchemaHelper.getInstanceByApi('/questionnaire/record');
  const tableRef = ref<any>(null);
  const columns = ['author', 'boName', 'phoneNumber', 'submitted', 'timeCost', 'fullScore', 'totalScore', 'source'];
  const queryParams = {
    formId: props.form.id,
    submitted: 1,
  };

  const viewVisible = ref(false);
  const currentRecord = ref(null);

  const handleView = (record: any) => {
    viewVisible.value = true;
    currentRecord.value = record;
  };

  const handleReject = async (record: any) => {
    Modal.confirm({
      title: '确认驳回',
      content: '确认驳回该答卷吗？',
      onOk: async () => {
        await questionnaireRecordReject({
          id: record.id,
        });
        tableRef.value.loadData();
      },
    });
  };

  const handleRowAction = (action: any, record: any) => {
    switch (action.key) {
      case 'detail':
        handleView(record);
        break;
      case 'reject':
        handleReject(record);
        break;
      default:
        break;
    }
  };
</script>

<template>
  <div class="mx-auto wrapper">
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="queryParams"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-boName="{ record }">
        <span>{{ record?.additionalInfo?.authorBoName }}</span>
      </template>
      <template #custom-column-phoneNumber="{ record }">
        <span>{{ record?.additionalInfo?.authorPhoneNumber }}</span>
      </template>
    </crud-table>

    <questionnaire-record-detail-modal v-model:visible="viewVisible" :record="currentRecord" />
  </div>
</template>

<style scoped lang="scss">
  .wrapper {
    width: 800px;
  }
</style>
