<script setup lang="ts">
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';

  const props = defineProps({
    formId: {
      type: Number,
      required: true,
    },
    questionId: {
      type: Number,
      required: true,
    },
  });

  const records = ref<any[]>([]);

  const handleOpen = async () => {
    const { data } = await request('/questionnaire/record', {
      baseURL: PROJECT_URLS.GO_PROJECT_API,
      params: {
        pageSize: 999,
        submitted: true,
        formId: props.formId,
      },
    });

    records.value = data.list
      .map((item: any) => {
        return {
          id: item.id,
          user: item.createdBy,
          attachments: JSON.parse(item.answers[props.questionId] || '[]'),
        };
      })
      ?.filter((item) => item.attachments?.length);
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="附件查看" :render-to-body="false" @open="handleOpen">
    <a-table size="mini" :data="records" :pagination="false">
      <template #columns>
        <a-table-column title="姓名" data-index="user">
          <template #cell="{ record }">
            {{ record.user?.name }}
          </template>
        </a-table-column>
        <a-table-column title="附件">
          <template #cell="{ record }">
            <attachments-preview-display :raw="record.attachments" />
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
