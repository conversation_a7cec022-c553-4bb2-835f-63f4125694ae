<script setup lang="ts">
  import StatisticsByAnswerGrouping from '@/views/manage/components/questionnaire/statisticsByAnswerGrouping.vue';
  import { ref } from 'vue';
  import QuestionnaireRecordList from '@/views/manage/components/questionnaire/questionnaireRecordList.vue';
  import QuestionnaireRecordTopList from '@/views/manage/components/questionnaire/questionnaireRecordTopList.vue';

  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
  });

  const activeKey = ref('1');

  const handleOpen = async () => {};
</script>

<template>
  <a-modal v-bind="$attrs" fullscreen :title="form?.name" hide-cancel ok-text="关闭" @open="handleOpen">
    <a-tabs v-if="form && $attrs.visible" v-model:active-key="activeKey">
      <a-tab-pane key="1" title="正确率">
        <template #title>
          <a-space>
            <IconBarChart />
            正确率
          </a-space>
        </template>
        <statistics-by-answer-grouping v-if="activeKey === '1'" :form="form" />
      </a-tab-pane>
      <a-tab-pane key="2" title="答卷列表">
        <template #title>
          <a-space>
            <IconEdit />
            答卷列表
          </a-space>
        </template>
        <questionnaire-record-list v-if="activeKey === '2'" :form="form" />
      </a-tab-pane>
      <a-tab-pane key="3" title="排行榜">
        <template #title>
          <a-space>
            <IconThunderbolt />
            排行榜
          </a-space>
        </template>
        <questionnaire-record-top-list v-if="activeKey === '3'" :form="form" />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<style scoped lang="scss">
  :deep .arco-tabs-nav-tab {
    justify-content: center;
  }
</style>
