<script setup lang="ts">
  import QuestionnaireFormPreview from '@repo/components/questionnaireForm/questionnaireFormPreview.vue';
  import { ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { secondsFormatter } from '@repo/ui/components/utils/utils';

  const props = defineProps({
    record: {
      type: Object,
      required: true,
    },
  });

  const form = ref();

  const handleOpen = async () => {
    const { data } = await request(`/questionnaire/form/${props.record.formId}`);

    form.value = data;
  };

  const handleClose = () => {
    form.value = null;
  };
</script>

<template>
  <a-modal v-bind="$attrs" title="问卷表单记录" hide-cancel @open="handleOpen" @close="handleClose">
    <a-descriptions v-if="$attrs.visible && record && form">
      <a-descriptions-item label="提交人">{{ record.createdBy?.name }}</a-descriptions-item>
      <a-descriptions-item label="用时">{{ secondsFormatter(record.timeCost) }}</a-descriptions-item>
      <a-descriptions-item label="总分">{{ record.totalScore }}</a-descriptions-item>
    </a-descriptions>
    <questionnaire-form-preview
      v-if="$attrs.visible && record && form"
      :answer="record.answers"
      :form="form"
      mode="resultView"
      :question-ids="form.questionIds"
      class="mt-4"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
