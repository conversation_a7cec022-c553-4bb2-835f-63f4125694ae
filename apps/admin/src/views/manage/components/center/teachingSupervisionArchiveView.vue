<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { getChapter, getChapterList } from '@repo/infrastructure/openapi/course';
  import TeachingArchiveDetail from '@repo/components/course/teachingArchive/teachingArchiveDetail.vue';
  import { request } from '@repo/infrastructure/request';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { AttachmentsPreviewDisplay } from '@repo/ui/components/data-display/components';
  import WebPrinter from '@repo/ui/components/data-display/webPrinter.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object as PropType<any>,
      required: true,
    },
    filters: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
  });

  const emit = defineEmits(['update:visible']);
  const leftSize = ref(250);
  const chapters = ref<any>([]);

  const coursesMap = ref<any>({});
  const course = ref<any>(null);
  const coursesStore = useSchoolCourseStore();
  const currentChapter = ref<any>({});
  const loading = ref(false);

  const printAreaId = computed(() => {
    if (!currentChapter.value?.id) {
      return null;
    }

    return `teaching-archive-detail-${currentChapter.value.id}`;
  });

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });

  const handleSwitchChapter = async (chapter) => {
    loading.value = true;
    try {
      currentChapter.value = chapter;
      const { data: courseData } = await request(`/course/course/${chapter.courseId}`);
      course.value = courseData;
      const { data } = await getChapter({
        id: chapter.id,
      });
      currentChapter.value = data;
    } finally {
      loading.value = false;
    }
  };

  const loadData = async () => {
    const { data } = await getChapterList({
      page: 1,
      pageSize: 999,
      teacherId: props.record.teacher.id,
      submitted: true,
      withParents: true,
      ...props.filters,
    });

    chapters.value = (data?.list || []).map((item) => {
      item.parentPath = item.parents
        ?.slice(1)
        .map((parent) => parent.name)
        .join(' / ');
      return item;
    });

    if (chapters.value.length) {
      await handleSwitchChapter(chapters.value[0]);
    }
  };

  const handleOpen = async () => {
    await loadData();
  };

  const handleClose = () => {
    currentChapter.value = {};
    course.value = null;
  };

  onMounted(async () => {
    coursesMap.value = await coursesStore.getSchoolCoursesMap();
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen @open="handleOpen" @close="handleClose">
    <template #title>{{ record?.teacher?.name }} 的教案</template>
    <a-split v-model:size="leftSize" class="split-wrapper">
      <template #first>
        <a-list size="small" class="mr-2">
          <a-list-item
            v-for="item in chapters"
            :key="item.id"
            class="cursor-pointer"
            @click="() => handleSwitchChapter(item)"
          >
            <a-list-item-meta :title="item.name">
              <template #title>
                <div :class="{ 'text-blue-600': currentChapter.id === item.id }">
                  {{ item.name }}
                </div>
                <div class="text-gray-400">
                  <small> {{ item.parentPath }} </small>
                </div>
                <div v-if="item.classTime" class="text-gray-400">
                  <small>上课时间： {{ item.classTime }} </small>
                </div>
              </template>
              <template #description>
                <small v-if="coursesMap[item.courseId]"> [{{ coursesMap[item.courseId].name }}] </small>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </a-list>
      </template>
      <template #second>
        <a-spin v-if="coursesMap" class="w-full" :loading="loading">
          <a-empty v-if="!currentChapter?.id" />
          <div v-else class="flex-1 bg-slate-400 py-6 paper-wrapper shadow-inner">
            <div :id="printAreaId" class="bg-white py-4 px-5 mx-auto page shadow-xl">
              <a-descriptions v-if="course?.id" title="课程信息" :column="4">
                <a-descriptions-item label="教师">{{ course.createdBy.name }}</a-descriptions-item>
                <a-descriptions-item label="科目">{{ coursesMap[course.category]?.name }}</a-descriptions-item>
                <a-descriptions-item label="年级">{{ course.grade }} {{ course.period }}</a-descriptions-item>
                <a-descriptions-item label="教材版本">{{ course.description }}</a-descriptions-item>
                <a-descriptions-item label="上课时间">{{ currentChapter?.classTime || '-' }}</a-descriptions-item>
                <a-descriptions-item v-if="currentChapter?.content?.lessonPrepareAttachments?.length" label="课件">
                  <attachments-preview-display :raw="currentChapter?.content?.lessonPrepareAttachments" />
                </a-descriptions-item>
                <a-descriptions-item label="引用教材说明">{{ currentChapter?.description || '-' }}</a-descriptions-item>
              </a-descriptions>
              <a-divider :margin="10" />
              <a-spin class="w-full">
                <teaching-archive-detail
                  v-if="currentChapter && currentChapter.content"
                  :chapter="currentChapter"
                  :show-assessment="false"
                />
                <a-empty v-else-if="!currentChapter" description="请先在左侧选择章节" />
                <a-empty v-else-if="currentChapter && !currentChapter.content" description="所选章节无内容" />
              </a-spin>
            </div>
          </div>
        </a-spin>
      </template>
    </a-split>
    <template #footer>
      <web-printer v-if="printAreaId" :selector="printAreaId" />
      <a-button type="primary" @click="modalVisible = false">完成</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  .split-wrapper {
    height: calc(100vh - 160px);
  }

  .paper-wrapper {
    .page {
      width: 1000px;
    }
  }
</style>
