<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';

  const emit = defineEmits(['currentChange', 'update:catalogList']);

  const props = defineProps({
    catalogList: {
      type: Array,
      default: () => [],
    },
  });

  const catalogs = computed({
    get: () => props.catalogList as any[],
    set: (value) => emit('update:catalogList', value || []),
  });

  const currentCatalog = ref<any>({
    id: -1,
  });

  const loadCatalogs = async () => {
    const { data } = await request('/guardian/courseCatalog', {
      params: {
        pageSize: 30,
      },
    });

    catalogs.value = data.list || [];
  };
  const handleStartEdit = (id: any) => {
    catalogs.value.forEach((catalog: any) => {
      catalog.editing = catalog.id === id;
      if (catalog.editing) {
        currentCatalog.value = { ...catalog };
      }
      return false;
    });
  };

  const handleCancelEdit = () => {
    currentCatalog.value = null;
    catalogs.value = catalogs.value
      .filter((catalog) => {
        return catalog.id;
      })
      .map((catalog) => {
        catalog.editing = false;
        return catalog;
      });
  };

  const handleSave = async () => {
    if (!currentCatalog.value.name) {
      Message.error('分类名称不能为空');
      return;
    }
    if (currentCatalog.value.name?.length > 4) {
      Message.error('分类名称不能超过4个字符');
      return;
    }
    if (currentCatalog.value.id) {
      await request(`/guardian/courseCatalog/${currentCatalog.value.id}`, {
        method: 'PUT',
        data: currentCatalog.value,
      });
    } else {
      await request('/guardian/courseCatalog', {
        method: 'POST',
        data: currentCatalog.value,
      });
    }

    handleCancelEdit();
    Message.success('保存成功');
    await loadCatalogs();
  };

  const handleDelete = async (id: any) => {
    await request(`/guardian/courseCatalog/${id}`, {
      method: 'DELETE',
    });

    Message.success('删除成功');
    await loadCatalogs();
  };

  const handleCurrentChange = (catalog: any) => {
    currentCatalog.value = catalog;
    emit('currentChange', catalog);
  };

  const handleAddNew = () => {
    catalogs.value.push({
      id: undefined,
      name: '新分类',
    });

    handleStartEdit(undefined);
  };
  onMounted(async () => {
    await loadCatalogs();
  });
</script>

<template>
  <a-card title="课程分类" class="w-70">
    <template #extra>
      <a-button type="text" size="mini" @click="handleAddNew">
        <template #icon>
          <IconPlus />
        </template>
        添加分类
      </a-button>
    </template>

    <a-space direction="vertical">
      <div class="cursor-pointer" @click="() => handleCurrentChange({ id: -1, name: '全部' })">
        <div
          :class="{
            'font-bold text-blue-500': currentCatalog?.id === -1,
          }"
        >
          全部
        </div>
      </div>
      <div v-for="catalog in catalogs" :key="catalog.id" @dblclick="() => handleStartEdit(catalog.id)">
        <a-space v-if="catalog.editing && currentCatalog" class="flex gap-2 mb-2">
          <a-input v-model="currentCatalog.name" size="mini" />
          <a-button-group>
            <a-button type="primary" size="mini" @click="handleSave">
              <IconCheck />
            </a-button>
            <a-button size="mini" @click="handleCancelEdit">
              <IconClose />
            </a-button>
            <a-popconfirm
              v-if="currentCatalog.id"
              content="确认删除该分类吗？"
              :ok-button-props="{ status: 'danger' }"
              @ok="() => handleDelete(currentCatalog.id)"
            >
              <a-button size="mini" status="danger">
                <IconDelete />
              </a-button>
            </a-popconfirm>
          </a-button-group>
        </a-space>
        <div
          v-else
          class="cursor-pointer"
          title="双击修改"
          :class="{
            'font-bold text-blue-500': currentCatalog?.id === catalog.id,
          }"
          @click="() => handleCurrentChange(catalog)"
        >
          {{ catalog.name }}
        </div>
      </div>
    </a-space>
  </a-card>
</template>

<style scoped lang="less"></style>
