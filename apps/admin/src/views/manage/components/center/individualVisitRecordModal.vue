<script setup lang="ts">
  import { computed, onMounted, PropType, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    record: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible']);

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value),
  });
  const schema = ref(null);
  const queryParams = computed(() => {
    return {
      sort: '-id',
      individualVisit: props.record.id,
    };
  });
  const individualVisitData = ref([]);
  // const loadData = () => {
  //   request(`/resourceCenter/individualVisitFollowUp/findByIndividualId/${props.record.id}`, {
  //     baseURL: PROJECT_URLS.MAIN_PROJECT_API,
  //   })
  //     .then((res) => {
  //       individualVisitData.value = res.data;
  //       console.log(res.data);
  //     })
  //     .catch((res) => {});
  // };
  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceCenter/individualVisitFollowUp');
  });
</script>

<template>
  <a-modal v-model:visible="modalVisible" fullscreen :render-to-body="false" popup-container="curd-table-detail-modal">
    <template #title> 【回访记录】 {{ record?.receptionDate }} {{ record?.studentName }} 的来访 </template>
    <table-with-modal-form
      v-if="schema && modalVisible && record"
      :data="individualVisitData"
      module-name="个案来访回访记录"
      :schema="schema"
      :default-query-params="queryParams"
      :default-edit-value="{ individualVisit: record }"
      :visible-columns="['followUpDate', 'questions', 'advice', 'ended']"
    />
  </a-modal>
</template>

<!--popupContainer: '#org-drop-down-parent-id',-->
<style scoped lang="scss"></style>
