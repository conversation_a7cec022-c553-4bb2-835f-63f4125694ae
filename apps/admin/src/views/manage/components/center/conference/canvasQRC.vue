<!--<script setup lang="ts">-->
<!--  import QRCode from 'qrcode';-->
<!--  import { ref, watch, onMounted, computed } from 'vue';-->
<!--  import { PROJECT_URLS } from '@repo/env-config';-->

<!--  const props = defineProps({-->
<!--    record: Object,-->
<!--    isFullscreen: {-->
<!--      type: Boolean,-->
<!--      default: false,-->
<!--    },-->
<!--  });-->

<!--  const title = computed(() => {-->
<!--    const text = props.record?.subject || '';-->
<!--    // 限制标题长度，避免过长-->
<!--    return text.length > 9 ? `${text.substring(0, 9)}...` : text;-->
<!--  });-->
<!--  const url = computed(() => `${PROJECT_URLS.MAIN_PROJECT}/module/meetingCheckIn.html?meetingId=${props.record?.id}`);-->
<!--  const qrCodeSize = computed(() => (props.isFullscreen ? 600 : 200));-->
<!--  const canvasRef = ref<HTMLCanvasElement | null>(null);-->

<!--  // 生成美观的二维码-->
<!--  const generateQRCode = async () => {-->
<!--    if (!canvasRef.value) return;-->

<!--    const size = qrCodeSize.value;-->
<!--    canvasRef.value.width = size;-->
<!--    canvasRef.value.height = size;-->

<!--    // 生成更美观的二维码-->
<!--    await QRCode.toCanvas(canvasRef.value, url.value, {-->
<!--      width: size,-->
<!--      margin: 4,-->
<!--      color: {-->
<!--        dark: '#3e3e3e',-->
<!--        light: '#ffffff',-->
<!--      },-->
<!--      errorCorrectionLevel: 'H',-->
<!--    });-->

<!--    const ctx = canvasRef.value.getContext('2d');-->
<!--    if (!ctx) return;-->

<!--    const text = title.value;-->
<!--    const fontSize = Math.max(12, size / 15);-->
<!--    ctx.font = `bold ${fontSize}px 'PingFang SC', 'Microsoft YaHei', sans-serif`;-->
<!--    ctx.textAlign = 'center';-->
<!--    ctx.textBaseline = 'middle';-->

<!--    const textWidth = ctx.measureText(text).width;-->
<!--    const maxWidth = size * 0.8;-->
<!--    const displayText =-->
<!--      textWidth > maxWidth ? `${text.substring(0, Math.floor((text.length * maxWidth) / textWidth))}...` : text;-->

<!--    // 绘制更美观的背景-->
<!--    const textHeight = fontSize * 1.5;-->
<!--    const padding = fontSize / 2;-->
<!--    const cornerRadius = 4;-->

<!--    const centerX = size / 2;-->
<!--    const centerY = size / 2;-->

<!--    ctx.beginPath();-->
<!--    ctx.roundRect(-->
<!--      centerX - Math.min(textWidth, maxWidth) / 2 - padding,-->
<!--      centerY - textHeight / 2,-->
<!--      Math.min(textWidth, maxWidth) + padding * 2,-->
<!--      textHeight,-->
<!--      cornerRadius,-->
<!--    );-->
<!--    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';-->
<!--    ctx.fill();-->

<!--    // 绘制文字-->
<!--    ctx.fillStyle = '#0069c3';-->
<!--    ctx.fillText(displayText, centerX, centerY);-->
<!--  };-->

<!--  onMounted(generateQRCode);-->
<!--  watch([url, title, qrCodeSize], generateQRCode);-->
<!--</script>-->

<!--<template>-->
<!--  <div class="qr-code-container">-->
<!--    <canvas-->
<!--      ref="canvasRef"-->
<!--      :style="{-->
<!--        width: `${qrCodeSize}px`,-->
<!--        height: `${qrCodeSize}px`,-->
<!--        borderRadius: '8px',-->
<!--        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',-->
<!--      }"-->
<!--    />-->
<!--  </div>-->
<!--</template>-->

<!--<style scoped>-->
<!--  .qr-code-container {-->
<!--    display: inline-block;-->
<!--    padding: 8px;-->
<!--    background: white;-->
<!--    border-radius: 12px;-->
<!--    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);-->
<!--  }-->
<!--</style>-->
