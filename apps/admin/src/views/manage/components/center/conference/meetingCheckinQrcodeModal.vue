<script setup lang="ts">
  import VueQrcode from '@chenfengyuan/vue-qrcode';
  import { computed, onMounted, ref, onUnmounted } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { VuePrintNext } from 'vue-print-next';

  const props = defineProps({
    record: {
      type: Object,
      default: null,
    },
    modelValue: {},
  });

  const emit = defineEmits(['update:modelValue']);
  const modalVisible = computed({
    get() {
      return props.modelValue;
    },
    set(val) {
      emit('update:modelValue', val);
    },
  });
  const isFullscreen = ref(false);
  const isSignIn = ref(true);
  const qrCodeSize = computed(() => {
    return isFullscreen.value ? '600px' : '200px';
  });

  const title = computed(() => {
    return props.record?.subject;
  });
  const resetData = () => {
    isFullscreen.value = false;
    isSignIn.value = true;
  };
  const currentTime = ref(Date.now());

  const url = computed(() => {
    const expiryTime = currentTime.value + 60 * 1000;
    return `${PROJECT_URLS.MAIN_PROJECT}/module/meetingCheckIn.html?meetingId=${props.record?.id}&isSignIn=${isSignIn.value}&title=${title.value}&expiry=${expiryTime}`;
  });

  const handlePrint = () => {
    try {
      // eslint-disable-next-line no-new
      new VuePrintNext({
        el: `#print-area`,
        popTitle: '',
        zIndex: 9999,
      });
    } finally {
      /**/
    }
  };
  const downloadQRCode = async () => {
    const qrCanvas = document.querySelector('#print-area canvas') as HTMLCanvasElement;
    if (!qrCanvas) {
      console.error('二维码未生成');
      return;
    }

    const qrImg = new Image();
    qrImg.src = qrCanvas.toDataURL('image/png');

    qrImg.onload = () => {
      const padding = 20;
      const titleFontSize = 15;
      const totalWidth = qrCanvas.width;
      const totalHeight = qrCanvas.height + padding + titleFontSize + 10;

      const finalCanvas = document.createElement('canvas');
      finalCanvas.width = totalWidth;
      finalCanvas.height = totalHeight;
      const ctx = finalCanvas.getContext('2d')!;

      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, totalWidth, totalHeight);

      ctx.drawImage(qrImg, 0, 0);

      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, qrCanvas.height + padding / 2, totalWidth, titleFontSize + 10);

      ctx.fillStyle = '#000000';
      ctx.font = `bold ${titleFontSize}px 'PingFang SC', 'Microsoft YaHei', sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(title.value || '', totalWidth / 2, qrCanvas.height + padding / 2 + titleFontSize / 2);

      // 导出图片
      const dataUrl = finalCanvas.toDataURL('image/png');
      const a = document.createElement('a');
      a.href = dataUrl;
      a.download = `${title.value || '二维码'}.png`;
      a.click();
    };
  };

  let timer;
  onMounted(() => {
    timer = setInterval(() => {
      currentTime.value = Date.now();
    }, 60 * 1000);
  });
  onUnmounted(() => {
    clearInterval(timer);
  });
</script>

<template>
  <a-modal v-bind="$attrs" v-model:visible="modalVisible" hide-cancel :fullscreen="isFullscreen" @close="resetData">
    <template #title>
      <div class="flex flex-grow justify-center pl-12">
        <span class="text-center mx-auto">{{ isSignIn ? '签到码' : '签退码' }}</span>
        <a-button size="mini" class="mr-6" @click="handlePrint">打印</a-button>
      </div>
    </template>

    <div v-if="record" class="flex justify-center items-center h-full">
      <div id="print-area" class="text-center flex flex-col justify-center items-center">
        <div>
          <vue-qrcode class="qrc" :value="url" :style="{ width: qrCodeSize, height: qrCodeSize }" />
        </div>
        <div>{{ title }}</div>
        <!--<canvasQRC :record="record" :is-fullscreen="isFullscreen" />-->
      </div>
    </div>
    <div v-if="true" class="mx-auto mt-4 justify-center items-center flex flex-col">
      <small>您也可以复制以下链接，使用其他二维码生成工具生成美化后的二维码：</small>
      <a-input v-model="url" size="mini" />
    </div>
    <template #footer>
      <div class="flex justify-end items-center space-x-2">
        <a-button size="mini" @click="isFullscreen = !isFullscreen">
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </a-button>
        <a-button size="mini" type="outline" :status="isSignIn ? 'normal' : 'danger'" @click="isSignIn = !isSignIn">
          {{ isSignIn ? '签到' : '签退' }}
        </a-button>
        <a-button size="mini" @click="downloadQRCode">下载图片</a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss">
  .ant-modal-body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  .qr-code-container {
    text-align: center;
  }

  @media print {
    body {
      margin: 0;
      padding: 0;
      background: white;
    }

    #print-area {
      width: 800px;
      height: 800px;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      page-break-inside: avoid;
      break-inside: avoid;
    }

    #print-area > div {
      margin: 10px 0;
    }
  }
</style>
