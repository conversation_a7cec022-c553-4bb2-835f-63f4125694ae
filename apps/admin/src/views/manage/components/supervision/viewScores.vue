<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import EvaluationVersion2 from '@repo/components/resourceRoom/evaluateVersion2.vue';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    // 指标
    evaluationIndicator: {
      type: Object,
    },
  });
  const emits = defineEmits(['update:modelValue']);

  const columns = ref([
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      render: (text) => text.rowIndex + 1 || 1,
      align: 'center',
    },
    { title: '修改人', dataIndex: 'cbName' },
    { title: '创建人', dataIndex: 'mbName' },
    { title: '已确认评分', dataIndex: 'confirmed', slotName: 'confirmed', align: 'center' },
    { title: '已提交', dataIndex: 'submitted', slotName: 'submitted', align: 'center' },
    { title: '总评分', dataIndex: 'totalScore', align: 'center' },
    { title: '评价类型', dataIndex: 'criterionType' },
    { title: '评分标准名称', dataIndex: 'criterionName' },
    { title: '评分标题', dataIndex: 'title' },
    { title: '学校', dataIndex: 'fusionSchool.name' },
    { title: '年度', dataIndex: 'year' },
    { title: '操作', slotName: 'operation', align: 'center' },
  ]);

  const data = ref({});
  const yearOptions = ref([]);
  const currentOption = ref();
  // const currentOption = ref<any>(props.student || {});

  const evaluationVisible = ref(false);
  const fusionSchool = ref();
  const currentRecord = ref();

  const loadData = async () => {
    try {
      const res = await request(
        `/resourceRoom/resourceRoomEvaluation/findByCompanyId/${props.evaluationIndicator.id}`,
        {
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          method: 'get',
        },
      );
      yearOptions.value = Object.keys(res.data).map((key, index) => ({
        label: key,
        value: key,
        key: res.data[key][0].id,
      }));

      data.value = res.data;
    } catch (error) {
      /**/
    }
  };

  const handleChange = (val) => {
    currentOption.value = yearOptions.value.find((option) => option.value === val);
  };

  const handleSelect = (val) => {
    // const raw = yearOptions.value.find((option) => option.value === val);
    // currentOption.value = val;
  };

  const handleView = (record: any) => {
    fusionSchool.value = {
      name: record.fusionSchool.name,
      id: record.fusionSchool.id,
    };
    currentRecord.value = record;
    evaluationVisible.value = true;
  };

  const resetData = () => {
    yearOptions.value = [];
    currentOption.value = null;
    data.value = {};
  };

  const handlePreOk = () => {
    resetData();
    emits('update:modelValue', false);
  };

  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };

  const handelPreOpen = async () => {
    await loadData();
  };

  const handleUpdate = async (val) => {
    await loadData();
  };
  onMounted(async () => {
    if (props.evaluationIndicator && props.evaluationIndicator.id) await loadData();
  });
</script>

<template>
  <a-modal
    :visible="visible"
    fullscreen
    :closable="false"
    :render-to-body="false"
    @cancel="handleCancel"
    @before-open="handelPreOpen"
  >
    <div>
      <span v-if="evaluationIndicator" class="mr-8">指标： {{ evaluationIndicator.name }}</span>
      <span>
        查看年度:
        <a-select
          v-model="currentOption"
          class="mb-2 w-52"
          style="width: auto"
          size="mini"
          placeholder="请选择查看年度"
        >
          <a-option
            v-for="item in yearOptions"
            :key="item.key"
            :value="item.value"
            :label="item.label"
            @click="() => handleSelect(item)"
          />
        </a-select>
      </span>
    </div>
    <a-table :columns="columns" :data="data[currentOption] || []" column-resizable :bordered="{ cell: true }">
      <template #operation="{ record }">
        <a-button class="mr-2" size="mini" :disabled="!record.submitted" @click="handleView(record)">查看</a-button>
        <a-button v-show="false" size="mini">评分</a-button>
      </template>
      <template #confirmed="{ record }">
        <span v-if="record.confirmed"><icon-check-circle class="yes" /></span>
        <span v-else><icon-close-circle class="no" /></span>
      </template>
      <template #submitted="{ record }">
        <span v-if="record.submitted"><icon-check-circle class="yes" /></span>
        <span v-else><icon-close-circle class="no" /></span>
      </template>
    </a-table>

    <template #footer="{}">
      <div class="mr-10">
        <a-button size="mini" @click="handleCancel">取消</a-button>
        <a-popconfirm content="是否确认？" type="warning" @ok="handlePreOk">
          <a-button type="primary" size="mini" class="ml-2">确认</a-button>
        </a-popconfirm>
      </div>
    </template>
    <!--    <div>{{ evaluationIndicator }}</div>-->
    <!--    <div>{{ currentRecord }}</div>-->
    <!--    <a-divider />-->
    <!--    <div>{{ currentOption }}</div>-->
    <!--    <div>{{ yearOptions }}</div>-->
  </a-modal>
  <EvaluationVersion2
    v-if="evaluationVisible"
    v-model="evaluationVisible"
    :visible="evaluationVisible"
    :fusion-school="fusionSchool"
    :evaluation-indicator="currentRecord"
    :type="'expert'"
    @update:model-value="handleUpdate"
  />
</template>

<style scoped lang="scss">
  .yes {
    color: #00cc00;
  }
  .no {
    color: red;
  }
</style>
