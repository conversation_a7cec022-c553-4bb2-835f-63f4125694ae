<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import { axiosInstance, request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import { Message } from '@arco-design/web-vue';
  // import UploaderModal from '@repo/ui/components/upload/uploaderModal.vue';
  import { getOrgNature } from '@repo/components/utils/utils';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    evaluationCriterion: {
      type: Object,
    },
    displayType: {
      type: String,
    },
  });
  const emits = defineEmits(['update:modelValue']);
  const data = ref([]);
  const animation = ref(false);
  const evaluationCriterion = ref<any>({ ...props.evaluationCriterion, enabled: false });
  const editContextVisible = ref(false);
  const editContext = ref<any>({
    secondary: '',
    tertiary: '',
    standard: '',
  });
  const editKey = ref({
    key: '',
    id: '',
  });
  const isLoading = ref(false);
  const loadingTip = ref('');
  const fileItem = ref();
  const typeOptions = ref([]);
  const handleAnimation = () => {
    animation.value = true;
  };

  const handleUploadImport = async (option: any) => {
    fileItem.value = option;
  };
  const quotes = ref(); // 所有可引用数据
  const selectedQuotes = ref(); // 所有可引用数据
  const columns = ref([
    { title: '二级指标', dataIndex: 'secondary' },
    { title: '三级指标', dataIndex: 'tertiary' },
    { title: '评价标准', dataIndex: 'standard' },
    { title: '数据引用', dataIndex: 'quote', slotName: 'quote' },
    { title: '操作', slotName: 'operation', width: 140 },
  ]);
  const criterionDetails = ref<Array<any>>([]);
  const navbar = ref([]);
  const handleRes = (treeRes: any) => {
    criterionDetails.value = [];
    data.value = treeRes;
    navbar.value = [];
    treeRes.forEach((item: any, index: any) => {
      navbar.value.push(item.target);
      // 确保 criterionDetails 为当前索引初始化
      if (!criterionDetails.value[index]) {
        criterionDetails.value[index] = [];
      }
      // 二级指标
      item.children.forEach((child) => {
        child.children.forEach((item2) => {
          criterionDetails.value[index].push({
            id: item2.id,
            secondary: child.target,
            tertiary: item2.target,
            standard: item2.criterion,
            score: item2.score,
            systemData: '',
            attachments: item2.attachments,
            reference: item2.reference,
          });
        });
      });
      // 排序
      criterionDetails.value[index].sort((a, b) => {
        if (a.secondary < b.secondary) return -1;
        if (a.secondary > b.secondary) return 1;
        return 0;
      });
    });
  };
  const getData = async () => {
    try {
      isLoading.value = true;
      loadingTip.value = '加载中...';
      const res = await request(`/resourceCenter/evaluationCriterion/getTree/${evaluationCriterion.value.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      handleRes(res.data);
      const { data: result } = await request('/resourceCenter/evaluationCriterion/getSystemDataOptions', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'GET',
      });
      quotes.value = result;
    } finally {
      isLoading.value = false;
    }
  };

  const getType = async () => {
    const result = await request('/resourceCenter/evaluationCriterion/getAllType', {
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      method: 'GET',
    });
    typeOptions.value = [];
    Object.keys(result.data).forEach((item) => {
      typeOptions.value.push({
        label: item,
        value: result.data[item],
      });
    });
  };
  const currentIndex = ref(0);
  const tabClick = (key: number) => {
    currentIndex.value = key;
  };
  const spanMethod = (line: any) => {
    const currentRow = line.rowIndex;
    const currentData = criterionDetails.value[currentIndex.value][currentRow];
    if (line.columnIndex === 0) {
      let rowspan = 1;
      // 向下检查后续行，统计相同的二级指标
      for (let i = currentRow + 1; i < criterionDetails.value[currentIndex.value].length; i += 1) {
        if (criterionDetails.value[currentIndex.value][i].secondary === currentData.secondary) {
          rowspan += 1;
        } else {
          break; // 一旦遇到不同的值就停止
        }
      }
      // 只在第一行显示合并
      if (rowspan > 1) {
        return { rowspan, colspan: 1 };
      }
    }
    // 其他列不需要合并
    return {};
  };
  const updateNodeById = (nodes, id, newData) => {
    let found = false;
    nodes.forEach((node) => {
      if (node.id === id) {
        Object.assign(node, newData);
        found = true; // 标记为已找到
        return;
      }
      if (node.children && node.children.length > 0) {
        found = updateNodeById(node.children, id, newData);
      }
    });
    return found; // 返回找到的结果
  };
  const handleCellClick = (record: any, column, ev, rowIndex) => {
    if (['评价标准', '三级指标'].includes(column.title)) {
      // 不允许编辑
      // 没提交可以编辑
      editKey.value.id = record.id;
      editContext.value = record;
      editContextVisible.value = true;
    }
  };
  const handlePreEditOk = async () => {
    /* 跟更新一级指标 */
    data.value[currentIndex.value].target = navbar.value[currentIndex.value];
    // 跟新二级指标
    data.value[currentIndex.value].children.forEach((item: any) => {
      item.children.forEach((item2: any) => {
        if (item2.id === editContext.value.id) {
          item2.target = editContext.value.tertiary;
          item2.criterion = editContext.value.standard;
          item.target = editContext.value.secondary; // 当前三级指标的二级指标
        }
      });
    });
    editContextVisible.value = false;
  };

  const handlePreEditCancel = () => {
    editContextVisible.value = false;
  };

  const handleAdd = async (record: any) => {
    data.value[currentIndex.value].children.forEach((item: any) => {
      item.children.forEach((second: any) => {
        if (second.id === record.id) {
          const item2 = ref({ ...second, id: null }); // 有必要直接保存这个... 就能拿到id  || criterion 可以考虑置为空
          request('/resourceCenter/evaluationCriterionDetail', {
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            method: 'post',
            data: item2.value,
          }).then((res) => {
            item2.value.id = res.data.id;
            item.children.push(item2.value);
            const dataList = data.value;
            handleRes(dataList);
          });
        }
      });
    });
  };

  const handleDeleted = async (record: any) => {
    try {
      await request(`/resourceCenter/evaluationCriterionDetail/${record.id}`, {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        method: 'delete',
      });
      data.value[currentIndex.value].children.forEach((item: any) => {
        item.children = item.children.filter((second: any) => {
          // return !(second.id === record.id || (record.tertiary === second.target && record.standard === second.criterion && second.id === null)
          return second.id !== record.id;
        });
        const dataList = data.value;
        handleRes(dataList);
      });
      Message.success('删除成功');
    } catch (e) {
      /**/
    }
  };
  const handleChange = (record: any) => {
    data.value[currentIndex.value].children.forEach((item: any) => {
      item.children.forEach((second: any) => {
        if (second.id === record.id) {
          Object.assign(second, record);
        }
      });
    });
  };

  // 其实可以写一个全局的resetData ...
  const resetData = () => {
    data.value = [];
    fileItem.value = null;
    criterionDetails.value = [];
    evaluationCriterion.value = {};
    currentIndex.value = 0;
    typeOptions.value = [];
  };

  // 需要保存指标，然后保存指标详情
  const handlePreOk = async () => {
    isLoading.value = true;
    loadingTip.value = '保存中...';
    if (evaluationCriterion.value.name && evaluationCriterion.value.description) {
      /**/
    } else {
      Message.warning('请完整填写指标信息');
      isLoading.value = false;
      return;
    }
    if (fileItem.value != null && data.value.length === 0)
      // 没数据就传文件
      try {
        evaluationCriterion.value.nature = getOrgNature();
        const res = await request('/resourceCenter/evaluationCriterion/save', {
          method: 'POST',
          baseURL: PROJECT_URLS.MAIN_PROJECT_API,
          data: evaluationCriterion.value,
        });
        if (res.data.id) {
          evaluationCriterion.value = res.data;
          const formData = new FormData();
          formData.set('file', fileItem.value.fileItem.file);
          await axiosInstance.post(`/resourceCenter/evaluationCriterion/importCriterion/${res.data.id}`, formData, {
            method: 'POST',
            baseURL: PROJECT_URLS.MAIN_PROJECT_API,
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          });
          Message.success('指标创建成功');

          await getData();
          // resetData();
          emits('update:modelValue', false);
        }
      } catch (e) {
        Message.error('指标创建失败');
      } finally {
        isLoading.value = false;
      }
    else if (data.value != null && data.value.length > 0) {
      // 直接保存列表数据
      const dataList = data.value;
      const submitData = {
        ...evaluationCriterion.value,
        details: dataList,
        nature: getOrgNature(),
      };
      const res = await request('/resourceCenter/evaluationCriterion/submit', {
        method: 'POST',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: submitData,
      });
      isLoading.value = false;
      Message.success('提交成功');
    } else {
      isLoading.value = false;
      Message.error('请上传导入指标');
    }
  };
  const handleCancel = () => {
    resetData();
    emits('update:modelValue', false);
  };
  const handlePreOpen = async () => {
    await getType();
    if (evaluationCriterion.value != null && evaluationCriterion.value.id) {
      await getData();
    }
  };
  onMounted(async () => {
    await getType();
    if (props.visible && evaluationCriterion.value.id) {
      await getData();
    }
  });

  watch(
    () => props.evaluationCriterion,
    async (newVal, oldVal) => {
      evaluationCriterion.value = { ...newVal };
      if (evaluationCriterion.value.id && props.visible) {
        await getType();
        await getData();
      }
    },
  );
</script>

<template>
  <a-modal
    :on-before-ok="handlePreOk"
    :visible="visible"
    :fullscreen="displayType !== 'add'"
    :closable="false"
    @cancel="handleCancel"
    @before-open="handlePreOpen"
  >
    <template #footer="{}">
      <div class="mr-10">
        <a-button size="mini" @click="handleCancel">返回</a-button>
        <a-popconfirm content="是否确认？" type="warning" @ok="handlePreOk">
          <a-button type="primary" size="mini" class="ml-2">提交</a-button>
        </a-popconfirm>
      </div>
    </template>
    <!--编辑的时候显示-->
    <a-form v-if="displayType !== 'add'" auto-label-width @submit="handlePreOk">
      <a-row class="mb-4">
        <a-col v-if="false" :span="6" class="mr-2">
          <a-form-item label="评价类型" :rules="[{ required: true }]">
            <a-select v-model="evaluationCriterion.type" placeholder="请选择评价类型" :options="typeOptions" />
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mr-2">
          <a-form-item label="名称" :rules="[{ required: true }]">
            <a-input v-model="evaluationCriterion.name" placeholder="请输入指标名称" />
          </a-form-item>
        </a-col>
        <a-col :span="6" class="mr-2">
          <a-form-item label="评价描述" :rules="[{ required: true }]">
            <a-input v-model="evaluationCriterion.description" placeholder="请输入描述" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="开始时间">
            <a-date-picker v-model="evaluationCriterion.startTime" />
          </a-form-item>
        </a-col>
        <a-col :span="4">
          <a-form-item label="结束时间">
            <a-date-picker v-model="evaluationCriterion.endTime" />
          </a-form-item>
        </a-col>
        <a-col :span="1" class="mr-2">
          <a-form-item label="是否启用">
            <a-switch v-model="evaluationCriterion.enabled" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <!--新增的时候显示-->
    <a-form v-else auto-label-width @submit="handlePreOk">
      <a-form-item label="名称" :rules="[{ required: true }]">
        <a-input v-model="evaluationCriterion.name" placeholder="请输入指标名称" class="w-80" />
      </a-form-item>
      <a-form-item label="评价描述" :rules="[{ required: true }]">
        <a-input v-model="evaluationCriterion.description" placeholder="请输入描述" class="w-80" />
      </a-form-item>
      <a-form-item label="开始时间">
        <a-date-picker v-model="evaluationCriterion.startTime" />
      </a-form-item>
      <a-row>
        <a-col :span="14">
          <a-form-item label="结束时间">
            <a-date-picker v-model="evaluationCriterion.endTime" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item>
            <div v-if="displayType === 'add'" class="flex justify-start">
              <a-upload :show-file-list="false" accept=".xlsx,.xls" :custom-request="handleUploadImport" class="ml-4">
                <template #upload-button>
                  <a-button size="mini" :disabled="fileItem" @click="handleAnimation">
                    <span v-if="fileItem">已有文件</span>
                    <span v-else>指标导入</span>
                  </a-button>
                </template>
              </a-upload>
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <!--    <div>{{ evaluationCriterion }}</div>
    <div>{{ editContext }}</div>
    <div>{{ typeOptions }}</div>-->

    <!--编辑得到时候本来就不显示-->
    <!--    <a-divider class="m-0" />
    <div v-if="data.length === 0" class="flex justify-end mt-2">
      <span v-if="fileItem" class="text-green-500">文件已存在</span>
      <span v-else class="text-red-500">文件不存在</span>
      <a-upload :show-file-list="false" accept=".xlsx,.xls" :custom-request="handleUploadImport" class="ml-4">
        <template #upload-button>
          <a-button size="mini" @click="handleAnimation">
            <IconImport />
            导入
          </a-button>
        </template>
      </a-upload>
    </div>-->

    <div v-if="isLoading" class="flex items-center justify-center">
      <a-spin :tip="loadingTip" />
    </div>

    <a-tabs v-if="displayType !== 'add'" :default-active-key="0" class="mt-4" @tab-click="tabClick">
      <a-tab-pane v-for="(v, k) in navbar" :key="k" :title="v" class="p-4">
        <a-table
          key="id"
          :pagination="false"
          :columns="columns"
          :data="criterionDetails[k]"
          column-resizable
          :bordered="{ cell: true }"
          :span-method="spanMethod"
          @cell-dblclick="handleCellClick"
          ><!--没提交可以编辑-->
          <template #quote="{ record }">
            <a-select
              v-model="record.reference"
              multiple
              placeholder="指定系统数据"
              allow-search
              allow-clear
              :options="quotes"
              @change="handleChange(record)"
            >
            </a-select>
          </template>
          <template #operation="{ record }">
            <a-button size="mini" class="mr-2" style="color: #4679fb" @click="handleAdd(record)">新增</a-button>
            <a-button size="mini" style="color: #ff4d4d" @click="handleDeleted(record)">删除</a-button>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
  <a-modal
    :visible="editContextVisible"
    :closable="false"
    width="800"
    :on-before-ok="handlePreEditOk"
    @cancel="handlePreEditCancel"
  >
    <span class="text-gray-400">一级指标</span>
    <a-textarea v-model="navbar[currentIndex]" />
    <span class="text-gray-400">二级指标</span>
    <a-textarea v-model="editContext.secondary" />
    <span class="text-gray-400">三级指标</span>
    <a-textarea v-model="editContext.tertiary" />
    <span class="text-gray-400">评价标准</span>
    <a-textarea v-model="editContext.standard" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
