<script setup lang="ts">
  import { computed, defineAsyncComponent, ref } from 'vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import { merge } from 'lodash';
  import PageConfigCmp from '@/views/manage/components/system/posterDesigner/pageConfigCmp.vue';
  import PosterWrapper from '@repo/components/poster/posterWrapper.vue';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getWidgetDisplayCmp } from '@repo/components/poster/utils.ts';

  const props = defineProps({
    record: {
      type: Object,
      default: null,
    },
  });

  const wrapperRef = ref(null);
  const pageConfig = ref({});
  const saving = ref(false);
  const pageScale = ref(1);

  const widgets = ref([]);
  const currentWidgetId = ref(null);
  const currentWidget = computed(() => {
    return widgets.value.find((widget) => widget.id === currentWidgetId.value);
  });

  const handleOpen = async () => {
    pageConfig.value = {};
    widgets.value = [];
    const { data } = await request(`/common/poster/${props.record.id}`, {
      method: 'GET',
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });

    pageConfig.value = merge(
      {
        pageWidth: 750,
        pageMargin: 20,
        backgroundColor: '#fff',
      },
      data.pageConfig || {},
    );
    widgets.value = (data.widgets || []).map((item) => {
      return {
        ...item,
        id: item.id || `w-${Date.now()}`,
      };
    });
  };

  const handleAddWidget = (type) => {
    let content = '';
    if (type === 'Text' || type === 'Rich') {
      content = '在此输入内容';
    }

    const currentIndex = widgets.value?.findIndex((widget) => widget.id === currentWidgetId.value);
    const newItem = {
      id: `w-${Date.now()}`,
      type,
      style: {
        padding: 0,
      },
      content,
    };

    if (currentIndex === -1) {
      widgets.value.push(newItem);
    } else {
      widgets.value.splice(currentIndex + 1, 0, newItem);
    }
  };

  const handleRemoveWidget = (idx) => {
    widgets.value.splice(idx, 1);
  };

  const handleWidgetBlur = (e) => {
    // check if e.target has .designer-wrapper class
    if (e.target.classList.contains('designer-wrapper')) {
      currentWidgetId.value = null;
    }
  };

  const getWidgetConfigCmp = (widget) => {
    switch (widget.type) {
      case 'Text':
        return defineAsyncComponent(() => import('./posterDesigner/textInput.vue'));
      case 'Image':
        return defineAsyncComponent(() => import('./posterDesigner/imageInput.vue'));
      case 'Rich':
        return defineAsyncComponent(() => import('./posterDesigner/richInput.vue'));
      case 'Video':
        return defineAsyncComponent(() => import('./posterDesigner/videoInput.vue'));
      case 'Attachments':
        return defineAsyncComponent(() => import('./posterDesigner/attachmentInput.vue'));
      default:
        return null;
    }
  };

  const handleSave = async () => {
    saving.value = true;
    try {
      await request(`/common/poster/${props.record.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...props.record,
          pageConfig: pageConfig.value,
          widgets: widgets.value,
        },
      });

      Message.success('保存成功');
      return true;
    } finally {
      saving.value = false;
    }
  };
</script>

<template>
  <a-modal
    v-bind="$attrs"
    fullscreen
    title="海报设计"
    :on-before-ok="handleSave"
    :ok-loading="saving"
    unmount-on-close
    @open="handleOpen"
  >
    <div v-if="record && $attrs.visible" ref="wrapperRef" class="designer-wrapper bg-slate-600">
      <div class="mx-auto flex gap-2 preview-wrapper">
        <div>
          <a-card class="w-32" size="mini">
            <a-space direction="vertical">
              <a-button @click="() => handleAddWidget('Image')">
                <template #icon>
                  <IconImage />
                </template>
                图片
              </a-button>
              <a-button @click="() => handleAddWidget('Video')">
                <template #icon><IconFileVideo /></template>
                视频
              </a-button>
              <a-button @click="() => handleAddWidget('Text')">
                <template #icon><IconFontColors /></template>
                文字
              </a-button>
              <a-button @click="() => handleAddWidget('Rich')">
                <template #icon><IconQuote /></template>
                段落
              </a-button>
              <a-button @click="() => handleAddWidget('Attachments')">
                <template #icon><IconAttachment /></template>
                附件
              </a-button>
            </a-space>
          </a-card>
        </div>
        <poster-wrapper v-model:page-config="pageConfig" class="preview-page bg-white">
          <a-empty v-if="!widgets?.length" description="请在左侧选择组件" />
          <vue-draggable v-model="widgets" handle=".sort-handle" :animation="200" ghost-class="ghost">
            <div
              v-for="(widget, idx) in widgets"
              :key="idx"
              class="widget hover:bg-slate-50 sort-handle cursor-move"
              @click.stop="() => (currentWidgetId = widget.id)"
            >
              <component :is="getWidgetDisplayCmp(widget)" :widget="widget" :page-config="pageConfig" />
              <a-space class="actions">
                <a-button v-if="currentWidgetId === widget.id" size="mini" type="primary" status="success">
                  <template #icon>
                    <IconCheck />
                  </template>
                </a-button>
                <!--                <a-button size="mini" class="sort-handle">-->
                <!--                  <template #icon>-->
                <!--                    <IconSort />-->
                <!--                  </template>-->
                <!--                </a-button>-->
                <a-popconfirm content="确定删除此元素吗？" @ok="() => handleRemoveWidget(idx)">
                  <a-button size="mini" status="danger" class="delete-btn">
                    <template #icon>
                      <IconDelete />
                    </template>
                  </a-button>
                </a-popconfirm>
              </a-space>
            </div>
          </vue-draggable>
        </poster-wrapper>

        <div class="flex-1">
          <a-card title="操作区">
            <template #extra>
              <a-space>
                <a-button v-if="currentWidgetId" size="mini" @click="currentWidgetId = null">
                  <template #icon>
                    <IconFile />
                  </template>
                  页面设置
                </a-button>
                <a-button type="primary" :loading="saving" size="mini" @click="handleSave">保存</a-button>
              </a-space>
            </template>
            <page-config-cmp v-if="!currentWidgetId" v-model:page-config="pageConfig" />
            <component :is="getWidgetConfigCmp(currentWidget)" v-else v-model="currentWidget" />
          </a-card>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="scss">
  .designer-wrapper {
    min-height: 100vh;
    padding: 30px 20px;
    .preview-wrapper {
      margin: 0 auto;
    }
    .preview-page {
      min-height: 100vh;
      transform-origin: 50% 0;
    }
    .widget {
      position: relative;
      .actions {
        position: absolute;
        top: 0;
        right: 0;
      }
      .delete-btn {
        opacity: 0.01;
        transition: opacity 0.3s;
      }
      &:hover {
        .delete-btn {
          opacity: 1;
        }
      }
    }
  }
  :deep .widget-content {
    min-height: 10px;
  }
</style>
