<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import CommonWidgetStyle from '@/views/manage/components/system/posterDesigner/commonWidgetStyle.vue';
  import { merge } from 'lodash';
  import { WangEditor } from '@repo/rich-editor';

  const props = defineProps({
    modelValue: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const editVisible = ref(false);
  const emit = defineEmits(['update:modelValue']);
  const defaults = {
    fontSize: 14,
    color: '#000000',
  };

  const data = computed({
    get: () => props.modelValue || {},
    set: (value) => {
      emit(
        'update:modelValue',
        merge(
          {
            style: {
              ...defaults,
            },
          },
          value,
        ),
      );
    },
  });

  const handleShowEdit = () => {
    editVisible.value = true;
  };
</script>

<template>
  <a-button size="mini" @click="handleShowEdit">
    <template #icon>
      <IconEdit />
    </template>
    点击修改内容
  </a-button>
  <a-form size="mini" auto-label-width>
    <a-divider :margin="20">组件样式</a-divider>
    <common-widget-style v-model="data.style" />
  </a-form>

  <a-modal v-model:visible="editVisible" title="内容编辑" :width="700" :esc-to-close="false" hide-cancel>
    <wang-editor v-if="editVisible" v-model="data.content" />
  </a-modal>
</template>

<style scoped lang="scss"></style>
