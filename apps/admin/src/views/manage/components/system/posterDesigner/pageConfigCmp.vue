<script setup lang="ts">
  import { computed } from 'vue';
  import { backgroundGradients } from '@repo/infrastructure/constants';

  const props = defineProps({
    pageConfig: {
      type: Object,
      default: null,
    },
  });

  const emit = defineEmits(['update:pageConfig']);

  const data = computed({
    get: () => props.pageConfig || {},
    set: (value) => {
      emit('update:pageConfig', value);
    },
  });

  const handleSelectBackground = (background: string) => {
    if (data.value.background === background) {
      data.value.background = '';
    } else {
      data.value.background = background;
    }
  };
</script>

<template>
  <a-form size="mini">
    <a-form-item label="页面宽度">
      <a-space>
        <a-input-number v-model="data.pageWidth" :step="50" :min="750" :max="1200" mode="button" />
        <a-tooltip content="背景颜色">
          <a-color-picker v-model="data.backgroundColor" size="mini" />
        </a-tooltip>
        <a-tooltip content="全局文字颜色">
          <a-color-picker v-model="data.color" size="mini" />
        </a-tooltip>
      </a-space>
    </a-form-item>
    <a-form-item label="页面边距">
      <a-input-number v-model="data.pageMargin" :min="0" :max="100" mode="button" />
    </a-form-item>
    <a-form-item label="背景渐变">
      <div class="flex flex-wrap gap-2">
        <div
          v-for="(item, idx) in backgroundGradients"
          :key="idx"
          :class="{ 'border-blue-600': data.background === item, 'border-slate-100': data.background !== item }"
          class="p-1 border rounded"
        >
          <div class="gradientPreview" :style="{ background: item }" @click="() => handleSelectBackground(item)" />
        </div>
      </div>
    </a-form-item>
  </a-form>
</template>

<style scoped lang="scss">
  .gradientPreview {
    width: 20px;
    background: #fff;
    height: 20px;
    cursor: pointer;
  }
</style>
