<script setup lang="ts">
  import TeacherLessonSchedule from '@repo/components/teacher/teacherLessonSchedule.vue';
  import { ref } from 'vue';

  defineProps({
    teachersMap: {
      type: Object,
      required: true,
    },
    weekdays: {
      type: Array,
      required: true,
    },
    lessonsMap: {
      type: Object,
      required: true,
    },
    teacherSchedule: {
      type: Array,
      required: true,
    },
    courseAssignments: {
      type: Object,
      required: true,
    },
    task: {
      type: Object,
      required: true,
    },
    gradeClassesMap: {
      type: Object,
      required: true,
    },
    idx: {
      type: [String, Number],
      required: true,
    },
    totalDayPeriods: {
      type: Number,
      required: true,
    },
  });

  const teacherScheduleVisible = ref(false);
  const currentTeacherId = ref(null);

  const handleShowTeacherSchedule = (teacherId) => {
    currentTeacherId.value = teacherId;
    teacherScheduleVisible.value = true;
  };

  const handleHideTeacherSchedule = () => {
    currentTeacherId.value = null;
    teacherScheduleVisible.value = false;
  };
</script>

<template>
  <table class="border-collapse border border-slate-400 w-full text-center">
    <thead>
      <tr>
        <th
          class="border border-slate-300 p-0 text-blue-500"
          :class="{ 'bg-red-500 text-white': !teachersMap[idx]?.name }"
        >
          <div class="text-xs scale-90 cursor-pointer" @click="() => handleShowTeacherSchedule(idx)">
            {{ teachersMap[idx]?.name || '未定义' }}
          </div>
        </th>
        <th
          v-for="d in 5"
          :key="d"
          class="border border-slate-300 bg-slate-100 p-0 text-xs scale-90"
          style="width: 15.5%"
        >
          {{ weekdays[d - 1] }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="dpi in totalDayPeriods" :key="dpi">
        <th class="border border-slate-300 text-xs scale-90">
          {{ dpi }}
        </th>
        <td v-for="wdi in 5" :key="wdi" style="width: 15.5%" class="border border-slate-300 p-0">
          <div v-if="teacherSchedule[dpi - 1][wdi - 1]?.lessonId" class="text-xs scale-90">
            {{ lessonsMap[teacherSchedule[dpi - 1][wdi - 1]?.lessonId].simpleName }}
          </div>
        </td>
      </tr>
    </tbody>
  </table>

  <a-modal
    v-if="currentTeacherId"
    v-model:visible="teacherScheduleVisible"
    title="教师课程安排"
    :width="800"
    hide-cancel
    @close="handleHideTeacherSchedule"
  >
    <teacher-lesson-schedule
      v-if="currentTeacherId"
      :course-assignments="courseAssignments"
      :teacher="teachersMap[currentTeacherId]"
      :task="task"
      :grade-classes-map="gradeClassesMap"
    />
  </a-modal>
</template>

<style scoped lang="scss"></style>
