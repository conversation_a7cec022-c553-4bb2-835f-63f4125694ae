<script setup lang="ts">
  import { computed, PropType } from 'vue';
  import SmsTemplateSettings from '@/views/manage/components/configuration/smsTemplateSettings.vue';

  type ConfigItemType = 'SmsTemplate';

  const props = defineProps({
    configItem: {
      type: String as PropType<ConfigItemType>,
      required: true,
    },
    modelValue: {
      type: [String, Number, Boolean, Array, Object] as PropType<any>,
      required: true,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const val = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });
</script>

<template>
  <sms-template-settings v-if="configItem" v-model="val" />
</template>

<style scoped lang="scss"></style>
