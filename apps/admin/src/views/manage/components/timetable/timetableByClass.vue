<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';
  import ArrangeResult from '@/views/manage/components/classSchedule/arrangeResult.vue';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { useRoute } from 'vue-router';
  import AdministrationClass from '@/views/manage/components/classSchedule/administrationClass.vue';
  import { Message } from '@arco-design/web-vue';
  import { range } from 'lodash';

  const props = defineProps({
    timetable: {
      type: Object,
      required: true,
    },
    teachersMap: {
      type: Object,
      required: true,
    },
    lessonsMap: {
      type: Object,
      required: true,
    },
    grades: {
      type: Array,
      required: true,
    },
    gradeClasses: {
      type: Array,
      required: true,
    },
  });

  const route = useRoute();
  const emit = defineEmits(['update:timetable', 'save']);

  const computedTask = computed({
    get: () => props.timetable,
    set: (value) => {
      emit('update:timetable', value);
    },
  });

  const gradeConditionsMap = computedTask.value.gradeConditions?.reduce((acc, item) => {
    acc[item.gradeId] = item;
    return acc;
  }, {});

  const gradeClassesTree = ref<any[]>([]);
  const gradeClassesMap = ref<any>({});
  const ready = ref(false);
  const currentGradeClass = ref<any>(null);
  const splitSize = ref(280);
  const arrangementResult = ref<any>({});
  const personSchedules = ref<any>({});
  const weekdays = ['一', '二', '三', '四', '五'];
  const menuStore = useUserMenuStore();
  const orgNature = menuStore.getCurrentMenuInfo(route)?.app?.label;

  const newGradeCondition = ref<any>({
    morningCount: 3,
    afternoonCount: 3,
  });

  const currentGradeCondition = computed(() => {
    if (!currentGradeClass.value?.id) {
      return null;
    }
    return gradeConditionsMap[currentGradeClass.value.grade.id];
  });

  const totalDayPeriods = computed(() => {
    if (!currentGradeCondition.value) {
      return 0;
    }
    return currentGradeCondition.value.morningCount + currentGradeCondition.value.afternoonCount;
  });

  const handleGradeClassSelected = (selectedKeys: string[], info: any) => {
    if (info.node.type === 'gradeClass') {
      currentGradeClass.value = info.node.id;
    }
  };

  const courseAssignments = computed(() => {
    return props.timetable.assignments || [];
  });

  const handleResultFormatted = (data) => {
    personSchedules.value = {};
    const result: any = {};
    data.forEach((item: any) => {
      const gradeCondition = gradeConditionsMap[item.gradeId];
      result[item.gradeClassId] = result[item.gradeClassId] || {};
      const dayPeriodIndex = item.dayPeriod === 0 ? item.classIndex : gradeCondition.morningCount + item.classIndex;
      result[item.gradeClassId][dayPeriodIndex] = result[item.gradeClassId][dayPeriodIndex] || {};
      result[item.gradeClassId][dayPeriodIndex][item.weekdayIndex] = item;

      personSchedules.value[item.gradeClassId] = personSchedules.value[item.gradeClassId] || [];
      personSchedules.value[item.gradeClassId].push(item);
    });

    arrangementResult.value = result;
  };

  /**
   * 获取教师的课表
   * {teacherId: [[1,2,3,4,5]]}
   */
  const getTeacherSchedule = computed(() => (gradeClassId) => {
    const result = {};
    personSchedules.value[gradeClassId]?.forEach((lesson: any) => {
      const dayPeriodIndex =
        lesson.dayPeriod === 0 ? lesson.classIndex : currentGradeCondition.value.morningCount + lesson.classIndex;
      result[lesson.teacherId] = result[lesson.teacherId] || [];
      if (!result[lesson.teacherId].length) {
        for (let i = 0; i < totalDayPeriods.value; i += 1) {
          result[lesson.teacherId].push([]);
        }
      }
      result[lesson.teacherId][dayPeriodIndex] = result[lesson.teacherId][dayPeriodIndex] || [];
      result[lesson.teacherId][dayPeriodIndex][lesson.weekdayIndex] =
        result[lesson.teacherId][dayPeriodIndex][lesson.weekdayIndex] || lesson;
    });

    return result;
  });

  onMounted(async () => {
    gradeClassesMap.value = props.gradeClasses.reduce((acc: any, item: any) => {
      acc[item.id] = item;
      return acc;
    }, {});

    const classesMap = props.gradeClasses.reduce((acc: any, item: any) => {
      if (!acc[item.grade?.id]) {
        acc[item.grade?.id] = [];
      }
      acc[item.grade?.id].push({
        id: item,
        key: `grade-class-${item.id}`,
        name: item.name,
        title: item.className,
        type: 'gradeClass',
        gradeId: item.grade?.id,
      });
      return acc;
    }, {});

    gradeClassesTree.value = props.grades.map((grade: any) => {
      return {
        id: grade.id,
        key: `grade-${grade.id}`,
        title: grade.name,
        children: classesMap[grade.id] || [],
        type: 'grade',
      };
    });

    ready.value = true;

    currentGradeClass.value = gradeClassesTree.value[0]?.children[0]?.id;

    handleResultFormatted(courseAssignments.value);
  });

  const handleManualAdjust = async (a: any, b: any, adjustedRow: any) => {
    const rawIndex = computedTask.value.assignments?.findIndex((item) => {
      return (
        item.gradeClassId === adjustedRow.gradeClassId &&
        item.weekdayIndex === adjustedRow.weekdayIndex &&
        item.classIndex === adjustedRow.classIndex &&
        item.dayPeriod === adjustedRow.dayPeriod
      );
    });

    if (rawIndex === -1) {
      Message.error('留空课或排课失败，无法手动调整 [0]');
      return;
    }

    const raw = computedTask.value.assignments[rawIndex];
    if (!raw) {
      Message.error('留空课或排课失败，无法手动调整 [1]');
      return;
    }

    computedTask.value.assignments[rawIndex] = {
      ...adjustedRow,
    };

    emit('save');

    handleResultFormatted(computedTask.value.assignments);
  };

  const handleManualCreateByClass = () => {
    const raw = [...courseAssignments.value].filter((item) => item.gradeClassId !== currentGradeClass.value.id);
    const newAssignments = [];

    const gradeClass = gradeClassesMap.value[currentGradeClass.value.id];
    let gradeCondition;
    if (!currentGradeCondition.value) {
      gradeCondition = {
        gradeId: gradeClass.grade.id,
        morningCount: newGradeCondition.value.morningCount,
        afternoonCount: newGradeCondition.value.afternoonCount,
      };

      gradeCondition.dayPeriodSequence = Array.from(
        { length: gradeCondition.morningCount + gradeCondition.afternoonCount },
        (_, index) => {
          const dayPeriod = index < gradeCondition.morningCount ? '上午' : '下午';
          const idx = index < gradeCondition.morningCount ? index : index - gradeCondition.morningCount;
          return {
            name: `${dayPeriod}第${idx + 1}节`,
            researchGroups: range(0, 5).map(() => {
              return null;
            }),
            types: range(0, 5).map(() => {
              return 'Normal';
            }),
            preferCourses: range(0, 5).map(() => {
              return [];
            }),
            fixedCourses: range(0, 5).map(() => {
              return null;
            }),
            ignoreCourses: range(0, 5).map(() => {
              return [];
            }),
          };
        },
      );
    } else {
      gradeCondition = currentGradeCondition.value;
    }

    for (let weekdayIndex = 0; weekdayIndex < 5; weekdayIndex += 1) {
      for (let i = 0; i < gradeCondition.morningCount; i += 1) {
        newAssignments.push({
          gradeClassId: gradeClass.id,
          gradeId: gradeCondition.gradeId,
          weekdayIndex,
          dayPeriod: 0,
          coordinate: {
            x: weekdayIndex,
            y: i,
            gradeClassId: gradeClass.id,
          },
          classIndex: i,
        });
      }
      for (let i = 0; i < gradeCondition.afternoonCount; i += 1) {
        newAssignments.push({
          gradeClassId: gradeClass.id,
          gradeId: gradeCondition.gradeId,
          weekdayIndex,
          dayPeriod: 1,
          coordinate: {
            x: weekdayIndex,
            y: i + gradeCondition.morningCount,
            gradeClassId: gradeClass.id,
          },
          classIndex: i,
        });
      }
    }

    const result = [
      ...raw,
      ...newAssignments.map((item) => {
        return {
          ...item,
        };
      }),
    ];

    const rawGradeConditions = computedTask.value.gradeConditions || [];
    const gradeConditions = rawGradeConditions.filter((item) => item.gradeId !== gradeCondition.gradeId);
    gradeConditions.push(gradeCondition);

    emit('update:timetable', {
      ...computedTask.value,
      assignments: result,
      gradeConditions,
    });
    emit('save', () => {
      handleResultFormatted(result);
    });
  };

  const handleResetTimetable = () => {
    const result = [...courseAssignments.value].filter((item) => item.gradeClassId !== currentGradeClass.value.id);
    emit('update:timetable', {
      ...computedTask.value,
      assignments: result,
    });
    emit('save', () => {
      handleResultFormatted(result);
    });
  };
</script>

<template>
  <div v-if="ready" class="m-2">
    <a-split v-model:size="splitSize" class="mt-2 split-container">
      <template #first>
        <a-tree
          :data="gradeClassesTree"
          show-line
          :default-selected-keys="[gradeClassesTree[0]?.children[0]?.id]"
          :selectable="true"
          :field-names="{ key: 'id' }"
          @select="handleGradeClassSelected"
        >
          <template #title="node">
            {{ node.name || node.title }}
          </template>
        </a-tree>
      </template>
      <template #second>
        <a-empty v-if="!currentGradeClass">在左侧选择班级以查看课表</a-empty>
        <div v-else class="flex gap-2">
          <arrange-result
            class="flex-1 border-r border-slate-200 px-2"
            :teachers-map="teachersMap"
            :lessons-map="lessonsMap"
            :grade-class="currentGradeClass"
            :arrange-result="arrangementResult"
            :grade-condition="currentGradeCondition"
            :check-row-id-when-adjust="false"
            mode="timetable"
            @manual-adjustment="handleManualAdjust"
          >
            <template #extra-actions>
              <a-space>
                <a-popconfirm content="确定要重置本班课表吗？" @ok="handleResetTimetable">
                  <a-button size="mini" type="outline">
                    <template #icon>
                      <IconRedo />
                    </template>
                    重置本班课表
                  </a-button>
                  <!--                  <template #content>-->
                  <!--                    <div> 确定要重置本班课表吗？ </div>-->
                  <!--                    <div class="flex flex-col mt-4">-->
                  <!--                      <a-form-item label="上午课时">-->
                  <!--                        <a-input-number-->
                  <!--                          v-model="newGradeCondition.morningCount"-->
                  <!--                          size="mini"-->
                  <!--                          :min="1"-->
                  <!--                          :max="5"-->
                  <!--                          mode="button"-->
                  <!--                          class="w-32"-->
                  <!--                        >-->
                  <!--                          <template #suffix>节</template>-->
                  <!--                        </a-input-number>-->
                  <!--                      </a-form-item>-->
                  <!--                      <a-form-item label="下午课时">-->
                  <!--                        <a-input-number-->
                  <!--                          v-model="newGradeCondition.afternoonCount"-->
                  <!--                          size="mini"-->
                  <!--                          :min="1"-->
                  <!--                          :max="5"-->
                  <!--                          mode="button"-->
                  <!--                          class="w-32"-->
                  <!--                        >-->
                  <!--                          <template #suffix>节</template>-->
                  <!--                        </a-input-number>-->
                  <!--                      </a-form-item>-->
                  <!--                    </div>-->
                  <!--                  </template>-->
                </a-popconfirm>
              </a-space>
            </template>
            <template #not-configured>
              <a-empty v-if="courseAssignments?.length">
                <div>当前班级尚未创建课表，请先点击创建</div>
                <a-space v-if="!currentGradeCondition" class="mt-2">
                  <a-form-item label="上午">
                    <a-input-number
                      v-model="newGradeCondition.morningCount"
                      size="mini"
                      :min="1"
                      :max="5"
                      mode="button"
                      class="w-32"
                    >
                      <template #suffix>节</template>
                    </a-input-number>
                  </a-form-item>
                  <a-form-item label="下午">
                    <a-input-number
                      v-model="newGradeCondition.afternoonCount"
                      size="mini"
                      :min="1"
                      :max="5"
                      mode="button"
                      class="w-32"
                    >
                      <template #suffix>节</template>
                    </a-input-number>
                  </a-form-item>
                </a-space>

                <div>
                  <a-button class="mt-2" size="mini" type="primary" @click="handleManualCreateByClass">
                    <template #icon> <IconPlus /> </template>创建课表
                  </a-button>
                </div>
              </a-empty>
            </template>
          </arrange-result>
          <a-card
            v-if="currentGradeClass?.id && currentGradeCondition"
            class="w-96"
            :bordered="false"
            :body-style="{ padding: '10px' }"
          >
            <template #title> 行政班视角 </template>
            <a-empty v-if="!getTeacherSchedule(currentGradeClass.id)" content="暂无排课结果"></a-empty>
            <div class="grid grid-cols-2 gap-2 mt-2">
              <administration-class
                v-for="(teacherSchedule, idx) in getTeacherSchedule(currentGradeClass.id)"
                :key="idx"
                :teacher-schedule="teacherSchedule"
                :teachers-map="teachersMap"
                :weekdays="weekdays"
                :lessons-map="lessonsMap"
                :course-assignments="courseAssignments"
                :grade-classes-map="gradeClassesMap"
                :task="computedTask"
                :idx="idx"
                :total-day-periods="totalDayPeriods"
              />
            </div>
          </a-card>
        </div>
      </template>
    </a-split>
  </div>
  <a-skeleton v-else animation>
    <a-skeleton-line :rows="5" />
  </a-skeleton>
</template>

<style scoped lang="scss">
  .split-container {
    height: calc(100vh - 145px);
  }
</style>
