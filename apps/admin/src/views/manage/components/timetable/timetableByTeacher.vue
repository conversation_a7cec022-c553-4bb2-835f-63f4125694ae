<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import TeacherLessonSchedule from '@repo/components/teacher/teacherLessonSchedule.vue';

  const props = defineProps({
    timetable: {
      type: Object,
      required: true,
    },
    teachers: {
      type: Array,
      required: true,
    },
    gradeClassesMap: {
      type: Object,
      required: true,
    },
    keyword: {
      type: String,
      default: '',
    },
  });

  const currentTeacher = ref(null);

  const ready = ref(false);
  const splitSize = ref(160);

  const handleTeacherSelected = (selectedKeys) => {
    currentTeacher.value = props.teachers.find((teacher) => teacher.id === selectedKeys[0]);
  };

  const allAvailableTeachersSet = computed(() => {
    return new Set(props.timetable.assignments.map((item) => item.teacherId));
  });
  const allAvailableTeachers = computed(() => {
    const all = props.teachers.filter((teacher) => allAvailableTeachersSet.value.has(teacher.id));
    if (props.keyword?.trim()) {
      return all.filter((teacher) => teacher.name.includes(props.keyword));
    }
    return all;
  });
  const teachersMap = computed(() => {
    return props.teachers.reduce((acc, teacher) => {
      acc[teacher.id] = teacher;
      return acc;
    }, {});
  });

  onMounted(async () => {
    handleTeacherSelected([allAvailableTeachers.value[0]?.id]);
    ready.value = true;
  });
</script>

<template>
  <div v-if="ready" class="m-2">
    <a-split v-model:size="splitSize" class="mt-2 split-container">
      <template #first>
        <a-tree
          :data="allAvailableTeachers"
          show-line
          :default-selected-keys="[allAvailableTeachers[0]?.id]"
          :selectable="true"
          :field-names="{ key: 'id' }"
          @select="handleTeacherSelected"
        >
          <template #title="node">
            {{ node.name || node.title }}
          </template>
        </a-tree>
      </template>
      <template #second>
        <a-empty v-if="!currentTeacher?.id">在左侧选择教师以查看课表</a-empty>
        <div v-else class="px-4 py-6 bg-slate-500 mx-4 h-full">
          <div class="bg-white py-20 px-10 mx-auto mt-10" style="width: 1000px">
            <div class="text-center pb-4 text-lg font-medium">
              {{ teachersMap[currentTeacher.id]?.name }} 的课表
              {{ timetable?.period ? `（${timetable.period}）` : '' }}
            </div>
            <teacher-lesson-schedule
              mode="full"
              :teacher="currentTeacher"
              :task="timetable"
              :highlight-today="false"
              :course-assignments="timetable.assignments"
              :grade-classes-map="gradeClassesMap"
            />
          </div>
        </div>
      </template>
    </a-split>
  </div>
  <a-skeleton v-else animation>
    <a-skeleton-line :rows="5" />
  </a-skeleton>
</template>

<style scoped lang="scss">
  .split-container {
    height: calc(100vh - 145px);
  }
</style>
