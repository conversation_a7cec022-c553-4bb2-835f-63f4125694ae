<script setup lang="ts">
  import { computed, PropType, ref } from 'vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { Message } from '@arco-design/web-vue';
  import { request } from '@repo/infrastructure/request';
  import { range } from 'lodash';

  const props = defineProps({
    visible: {
      type: Boolean,
      required: true,
    },
    grades: {
      type: Array as PropType<any[]>,
      required: true,
    },
    gradeClasses: {
      type: Array as PropType<any[]>,
      required: true,
    },
    schoolId: {
      type: Number,
      required: true,
    },
  });

  const emit = defineEmits(['update:visible', 'ok']);

  const modalVisible = computed({
    get() {
      return props.visible;
    },
    set(val) {
      emit('update:visible', val);
    },
  });

  const period = ref('');
  const gradeConditions = ref([]);
  const weekdays = ['周一', '周二', '周三', '周四', '周五'];

  const handleOpen = () => {
    modalVisible.value = true;
    gradeConditions.value = props.grades.map((grade) => ({
      gradeId: grade.id,
      gradeName: grade.name,
      morningCount: 3,
      afternoonCount: 3,
    }));
  };

  const saving = ref(false);
  const handleCreate = async () => {
    if (!period.value) {
      Message.error('请选择学期');
      return false;
    }
    if (!gradeConditions.value?.length) {
      Message.error('请先创建年级，并填写年级课时');
      return false;
    }
    if (gradeConditions.value.some((condition) => condition.morningCount === 0 || condition.afternoonCount === 0)) {
      Message.error('请填写上午和下午课时');
      return false;
    }

    const gradesMap = gradeConditions.value.reduce((map, grade) => {
      map[grade.gradeId] = grade;
      return map;
    }, {});

    const assignments = [];

    props.gradeClasses.forEach((gradeClass) => {
      const grade = gradesMap[gradeClass.grade.id];
      if (grade) {
        for (let weekdayIndex = 0; weekdayIndex < 5; weekdayIndex += 1) {
          for (let i = 0; i < grade.morningCount; i += 1) {
            assignments.push({
              gradeClassId: gradeClass.id,
              gradeId: grade.gradeId,
              weekdayIndex,
              dayPeriod: 0,
              coordinate: {
                x: weekdayIndex,
                y: i,
                gradeClassId: gradeClass.id,
              },
              classIndex: i,
            });
          }
          for (let i = 0; i < grade.afternoonCount; i += 1) {
            assignments.push({
              gradeClassId: gradeClass.id,
              gradeId: grade.gradeId,
              weekdayIndex,
              dayPeriod: 1,
              coordinate: {
                x: weekdayIndex,
                y: i + grade.morningCount,
                gradeClassId: gradeClass.id,
              },
              classIndex: i,
            });
          }
        }
      }
    });

    const getDayPeriodSequence = (gradeCondition) => {
      return Array.from({ length: gradeCondition.morningCount + gradeCondition.afternoonCount }, (_, index) => {
        const dayPeriod = index < gradeCondition.morningCount ? '上午' : '下午';
        const idx = index < gradeCondition.morningCount ? index : index - gradeCondition.morningCount;
        return {
          name: `${dayPeriod}第${idx + 1}节`,
          researchGroups: range(0, 5).map(() => {
            return null;
          }),
          types: range(0, 5).map(() => {
            return 'Normal';
          }),
          preferCourses: range(0, 5).map(() => {
            return [];
          }),
          fixedCourses: range(0, 5).map(() => {
            return null;
          }),
          ignoreCourses: range(0, 5).map(() => {
            return [];
          }),
        };
      });
    };

    saving.value = true;

    // console.log(
    //   gradeConditions.value.map((item) => {
    //     return {
    //       ...item,
    //       dayPeriodSequence: getDayPeriodSequence(item),
    //     };
    //   }),
    // );
    //
    // return false;

    try {
      const { data } = await request('/teacher/timetable', {
        method: 'POST',
        data: {
          gradeConditions: gradeConditions.value.map((item) => {
            return {
              ...item,
              dayPeriodSequence: getDayPeriodSequence(item),
            };
          }),
          schoolId: props.schoolId,
          period: period.value,
          active: false,
          assignments,
        },
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      Message.success('创建手动排课成功');
      modalVisible.value = false;
      emit('ok', data);
      return true;
    } finally {
      saving.value = false;
    }
  };
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="手动创建课表"
    :on-before-ok="handleCreate"
    :ok-loading="saving"
    @open="handleOpen"
  >
    <a-select v-model="period" placeholder="请选择学期" :options="getPeriodsList()"></a-select>
    <a-table size="mini" :data="gradeConditions" :pagination="false" class="mt-2">
      <template #columns>
        <a-table-column title="年级" data-index="gradeName" />
        <a-table-column title="上午课时" data-index="morningCount">
          <template #cell="{ record }">
            <a-input-number v-model="record.morningCount" class="w-24" mode="button" size="mini" :min="1" :max="5" />
          </template>
        </a-table-column>
        <a-table-column title="下午课时" data-index="afternoonCount">
          <template #cell="{ record }">
            <a-input-number v-model="record.afternoonCount" class="w-24" mode="button" size="mini" :min="1" :max="5" />
          </template>
        </a-table-column>
      </template>
    </a-table>
  </a-modal>
</template>

<style scoped lang="scss"></style>
