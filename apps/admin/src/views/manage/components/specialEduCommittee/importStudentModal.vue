<script setup lang="ts">
  import { computed } from 'vue';
  import { request } from '@repo/infrastructure/request';
  import { PROJECT_URLS } from '@repo/env-config';

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
  });
  const emits = defineEmits(['update:visible']);

  const modelValue = computed({
    get: () => {
      return props.visible;
    },
    set: (val) => {
      emits('update:visible', val);
    },
  });

  const customRequest = async (option) => {
    const { onProgress, onError, onSuccess, fileItem, name } = option;

    const formData = new FormData();
    formData.append('file', fileItem.file, fileItem.name);

    try {
      await request({
        url: `/specialCommittee/placementReport/importWaitingPlacementStudent`,
        method: 'Post',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: formData,
        onUploadProgress: (event) => {
          onProgress(Math.round((event.loaded / event.total) * 100));
        },
      });
    } catch (error) {
      console.error('File upload failed:', error);
    }
  };
</script>

<template>
  <a-modal v-model:visible="modelValue" title="导入待安置学生">
    <a-upload draggable accept=".xls,.xlsx" :custom-request="customRequest"></a-upload>
    <template #footer>
      <div class="flex justify-between">
        <a-button size="mini">下载模板</a-button>
        <a-button size="mini" type="primary">完成 </a-button>
      </div>
    </template>
  </a-modal>
</template>

<style scoped lang="scss"></style>
