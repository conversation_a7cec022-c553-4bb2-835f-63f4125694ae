<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { getPeriodsList } from '@repo/infrastructure/utils/scaffolds';
  import { useList } from '@repo/infrastructure/hooks';
  import TeachingSupervisionArchiveView from '@/views/manage/components/center/teachingSupervisionArchiveView.vue';
  import { getOrgNature } from '@repo/components/utils/utils';
  import { AvatarDisplay } from '@repo/ui/components/data-display/components';

  const pageFilters = ref<any>({});
  const periodsList = getPeriodsList({
    from: 2023,
  });

  const detailVisible = ref(false);
  const currentRow = ref<any>({});

  const {
    listData,
    queryParams,
    loading,
    loadData,
    listInit,
    pagination,
    handlePageChange,
    handlePageSizeChange,
    handleSorterChange,
  } = useList({
    api: '/teacher/teachingSupervision',
    defaultQueryParams: {
      orgNature: getOrgNature(),
    },
  });

  const handleLoadData = async () => {
    queryParams.value = {
      ...queryParams.value,
      ...pageFilters.value,
    };
    await loadData();
  };

  const handleView = (record) => {
    currentRow.value = record;
    detailVisible.value = true;
  };

  const getCurrentPeriod = (): string => {
    const now = new Date();
    const month = now.getMonth() + 1;
    const year = ref(now.getFullYear());
    const season = month >= 9 || month <= 2 ? '秋' : '春';
    if (month >= 1 && month <= 2) year.value -= 1;
    return `${year.value}年${season}季学期`;
  };

  watch(() => pageFilters.value, handleLoadData, { deep: true });

  onMounted(async () => {
    // const now = new Date();
    // const month = now.getMonth() + 1;
    // const year = now.getFullYear();
    pageFilters.value.period = getCurrentPeriod(); // `${year}年${month > 7 || month < 2 ? '秋' : '春'}季学期`;
    await listInit();
    await handleLoadData();
  });
</script>

<template>
  <a-card title="教学督导">
    <template #extra>
      <a-space>
        <a-select
          v-model="pageFilters.period"
          allow-search
          :options="periodsList"
          size="mini"
          placeholder="请选择学期"
        />
        <a-range-picker
          v-model="pageFilters.classDate"
          size="mini"
          format="YYYY-MM-DD HH:mm:ss"
          show-time
          :time-picker-props="{ defaultValue: ['08:00:00', '17:30:00'] }"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
        <a-button size="mini" @click="loadData">
          <template #icon>
            <IconRefresh />
          </template>
        </a-button>
      </a-space>
    </template>
    <a-table
      :data="listData"
      size="mini"
      :pagination="pagination"
      :loading="loading"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @sorter-change="handleSorterChange"
      @selection-change="onSelectionChange"
    >
      <template #columns>
        <a-table-column title="#" :width="50">
          <template #cell="{ rowIndex }">{{ rowIndex + 1 }}</template>
        </a-table-column>
        <a-table-column title="学校" data-index="school">
          <template #cell="{ record }">
            <a-tooltip :content="record.school?.name">
              <span>{{ record.school?.name }}</span>
            </a-tooltip>
          </template>
        </a-table-column>
        <a-table-column title="教师姓名" data-index="teacher">
          <template #cell="{ record }">
            <avatar-display :user-info="record.teacher" mode="capsule" />
          </template>
        </a-table-column>
        <a-table-column title="任教班级" data-index="gradeNames">
          <template #cell="{ record }">
            <a-space class="flex-wrap">
              <a-tag v-for="(item, idx) in record.teachingClasses" :key="idx" size="small">
                {{ item.name }}
              </a-tag>
            </a-space>
          </template>
        </a-table-column>
        <a-table-column title="任教年级" data-index="gradeClassNames">
          <template #cell="{ record }">
            <a-space class="flex-wrap">
              <a-tag v-for="(item, idx) in record.teachingGrades" :key="idx" size="small">
                {{ item.name }}
              </a-tag>
            </a-space>
          </template>
        </a-table-column>
        <a-table-column title="已提交教案" data-index="submittedCount" :width="110">
          <template #cell="{ record }">
            <a-tag
              v-if="record.weekScheduleCount > 0 && record.submittedCount >= record.weekScheduleCount"
              size="mini"
              color="green"
            >
              {{ record.submittedCount }} 份
            </a-tag>
            <a-tag
              v-else-if="record.weekScheduleCount <= 0 && record.submittedCount > record.weekScheduleCount"
              size="mini"
              color="blue"
            >
              {{ record.submittedCount }} 份
            </a-tag>
            <a-tag v-else-if="record.weekScheduleCount == 0 && record.submittedCount == 0" size="mini">
              {{ record.submittedCount }} 份
            </a-tag>
            <a-tag v-else size="mini" color="red"> {{ record.submittedCount }} 份 </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="周课时" data-index="originalCount" :width="80">
          <template #cell="{ record }">
            <a-tag size="mini"> {{ record.weekScheduleCount || '未排课' }} </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="原创" data-index="originalCount" :width="80">
          <template #cell="{ record }">
            <a-tag size="mini"> {{ record.originalCount }} 份 </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="引用" data-index="referenceCount" :width="80">
          <template #cell="{ record }">
            <a-tag size="mini"> {{ record.referenceCount }} 份 </a-tag>
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="80">
          <template #cell="{ record }">
            <a-button size="mini" @click="() => handleView(record)">查看</a-button>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <teaching-supervision-archive-view v-model:visible="detailVisible" :record="currentRow" :filters="pageFilters" />
  </a-card>
</template>

<style scoped lang="scss"></style>
