<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import TeacherSelect from '@repo/components/org/teacherSelect.vue';
  import { nextTick, onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';
  import { cloneDeep } from 'lodash';
  import { useLoading } from '@repo/infrastructure/hooks';
  import { request } from '@repo/infrastructure/request';
  import { Message } from '@arco-design/web-vue';
  import { PROJECT_URLS } from '@repo/env-config';
  import useSchoolCourseStore from '@repo/components/store/schoolCourseStore';
  import { getAdminClientNature } from '@repo/components/utils/utils';

  const schema = ref(null);

  const courseStore = useSchoolCourseStore();

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);
  const teacherListVisible = ref(false);
  const currentRow = ref(null);
  const tableRef = ref(null);
  const { loading, setLoading } = useLoading();
  const teacherSelectRef = ref(null);
  const teachingSubjects = ref([]);

  const queryParams = {
    schoolNature: menuInfo.app.label,
    sort: '-id',
    orgNature: getAdminClientNature(),
  };

  const handleRowAction = async (action: Record<string, any>, record: any) => {
    switch (action.key) {
      case 'teacherListMaintain':
        currentRow.value = cloneDeep(record);
        teacherListVisible.value = true;
        break;
      default:
        break;
    }
  };

  const handleClose = () => {
    teacherListVisible.value = false;
    currentRow.value = null;
  };

  const handleSelectTeacher = (id, teacher: any) => {
    const exists = currentRow.value.teacherList.find((item) => item.id === teacher.id);
    if (exists) {
      Message.error('该教师已存在');
      return;
    }
    currentRow.value.teacherList.push({
      id: teacher.id,
      name: teacher.name,
      fixed: false,
      courses: [],
      remark: '',
    });
  };

  const handleDeleteTeacher = (record) => {
    currentRow.value.teacherList = currentRow.value.teacherList.filter((item) => item.id !== record.id);
  };

  const handleSaveTeachersList = async () => {
    setLoading(true);
    try {
      await request(`/resourceRoom/gradeClass/${currentRow.value.id}`, {
        method: 'PUT',
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
        data: {
          ...currentRow.value,
        },
      });
      Message.success('修改任课教师成功');
      tableRef.value.handleLoadData();
      return true;
    } catch (e) {
      Message.error('修改任课教师失败');
      return false;
    } finally {
      setLoading(false);
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/resourceRoom/gradeClass');
    const rawCourses = await courseStore.getSchoolCourses();
    teachingSubjects.value = rawCourses?.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    ref="tableRef"
    module-name="班级"
    :schema="schema"
    :default-query-params="queryParams"
    :visible-columns="['fusionSchool', 'grade', 'name', 'managerUser', 'graduated', 'createdDate']"
    @row-action="handleRowAction"
  >
    <a-modal
      v-model:visible="teacherListVisible"
      :width="800"
      title="任课教师管理"
      :on-before-ok="handleSaveTeachersList"
      :ok-loading="loading"
      @close="handleClose"
    >
      <teacher-select
        v-if="currentRow"
        ref="teacherSelectRef"
        :default-query-params="{
          branchOffice: currentRow.grade?.school?.branchOfficeId,
          orgNature: currentRow?.orgNature,
        }"
        @change="handleSelectTeacher"
      />
      <a-table :data="currentRow?.teacherList || []" class="mt-2" :pagination="false">
        <template #columns>
          <a-table-column title="教师" data-index="name"></a-table-column>
          <a-table-column title="班主任" data-index="fixed">
            <template #cell="{ record }">
              <a-switch v-model="record.fixed" size="small" type="round" />
            </template>
          </a-table-column>
          <a-table-column title="备注" data-index="remark">
            <template #cell="{ record }">
              <a-input v-model="record.remark" size="mini" />
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <a-button type="text" size="mini" @click="() => handleDeleteTeacher(record)"> 删除 </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <div class="p-2 bg-gray-100 mt-2 text-sm text-blue-700 font-bold">
        * 如选择为固定任课教师，将在排课时优先安排该教师
      </div>
    </a-modal>
  </table-with-modal-form>
</template>

<style scoped lang="scss"></style>
