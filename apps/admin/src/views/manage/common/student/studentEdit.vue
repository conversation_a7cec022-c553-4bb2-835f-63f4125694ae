<script setup lang="ts">
  import { useRoute } from 'vue-router';
  import { PROJECT_URLS } from '@repo/env-config';

  const route = useRoute();
  const { id } = route.query;

  const frameSrc = `${PROJECT_URLS.MAIN_PROJECT}/centerManage.html#/resourceRoom/student/${id ? `edit/${id}` : 'add'}`;
</script>

<template>
  <iframe :src="frameSrc" class="frame" allow="geolocation" />
</template>

<style scoped lang="less">
  .frame {
    width: 100%;
    height: calc(100vh - 50px);
    border: none;
  }
</style>
