<script setup lang="ts">
  import StudentList from '@repo/components/student/studentList.vue';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';

  defineProps({
    modulePath: {
      type: String,
      required: true,
    },
  });

  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const queryParams = {
    schoolNature: menuInfo.app.label,
  };
</script>

<template>
  <student-list :title="menuInfo.app.label" :module-path="modulePath" :default-query-params="queryParams" />
</template>

<style scoped lang="scss"></style>
