<script setup lang="ts">
  import TableWithModalForm from '@repo/ui/components/table/tableWithModalForm.vue';
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';

  const schema = ref(null);
  const route = useRoute();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/evaluation/evaluationCategory');
  });
</script>

<template>
  <table-with-modal-form
    v-if="schema"
    module-name="评估分类"
    :schema="schema"
    :default-query-params="{ sort: '-id', orgNature: menuInfo.app.label }"
    :default-edit-value="{ orgNature: menuInfo.app.label }"
    :visible-columns="['name', 'createdDate']"
  />
</template>
