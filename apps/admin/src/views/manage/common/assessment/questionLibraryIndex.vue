<script setup lang="ts">
  import { QuestionLibrary } from '@repo/components/question-library';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserMenuStore } from '@repo/infrastructure/store';

  const route = useRoute();
  const router = useRouter();
  const menuStore = useUserMenuStore();
  const menuInfo = menuStore.getCurrentMenuInfo(route);

  const handleGoEditQuestion = (item?: any) => {
    if (item?.id) {
      router.push(`/manage/${menuInfo.app.key}/assessment/questionLibrary/edit?id=${item.id}`);
    } else {
      router.push(`/manage/${menuInfo.app.key}/assessment/questionLibrary/edit`);
    }
  };
</script>

<template>
  <question-library
    :default-edit-data="{ orgNature: menuInfo.app.label }"
    :default-query-params="{ orgNature: menuInfo.app.label }"
    @go-edit-question="handleGoEditQuestion"
  />
</template>

<style scoped lang="scss"></style>
