<script lang="ts" setup>
  import DefaultLayout from '@/common/layout/default-layout.vue';
  import NavCenterTabs from '@/common/layout/components/navCenterTabs.vue';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@repo/infrastructure/store';
  import { onBeforeMount, ref } from 'vue';

  const userStore = useUserStore();
  const tabs = [
    {
      key: 'drive',
      title: '云盘共享',
      visible() {
        return userStore.isAuthorized('admin_resource:drive');
      },
    },
    {
      key: 'digital',
      title: '数字资源',
      visible() {
        return userStore.isModuleAuthorized('数字资源');
      },
    },
    {
      key: 'intelligence',
      title: '培智资源',
      visible() {
        return false;
      },
    },
  ].filter((tab) => tab.visible?.() ?? true);

  const router = useRouter();
  const activeTab = ref<any>('');

  const handleTabsChange = (key: string) => {
    activeTab.value = key;
    router.push(`/resource/${key}`);
  };

  onBeforeMount(() => {
    const currentTab = router.currentRoute.value.path.split('/')[2];
    if (currentTab) {
      handleTabsChange(currentTab);
    } else {
      handleTabsChange(tabs.filter((tab) => tab.visible)[0].key);
    }
  });
</script>

<template>
  <default-layout>
    <template #page-title>特教资源</template>
    <template #navbar-center>
      <nav-center-tabs :active="activeTab" :tabs="tabs" :width="330" @change="handleTabsChange" />
    </template>
    <div class="main-wrapper">
      <router-view />
    </div>
  </default-layout>
</template>

<style lang="less" scoped>
  .main-wrapper {
    margin: 10px;
  }
</style>
