<!--<script lang="ts" setup>-->
<!--  import { ref } from 'vue';-->
<!--  import { useRouter } from 'vue-router';-->
<!--  import { COURSE_CATEGORIES, COURSE_GRADES, COURSE_PERIODS } from '@repo/infrastructure/constants';-->
<!--  import CreateCourseModal from '@/views/course/components/createCourseModal.vue';-->
<!--  import CourseList from '@/views/course/components/courseList.vue';-->

<!--  const createCourseModalVisible = ref<any>(false);-->
<!--  const currentRow = ref<any>({});-->
<!--  const router = useRouter();-->
<!--  const courseListRef = ref<any>();-->
<!--  const handleShowEditCourse = (course) => {-->
<!--    currentRow.value = course;-->
<!--    createCourseModalVisible.value = true;-->
<!--  };-->
<!--  const handleCreateCourseOk = async () => {-->
<!--    createCourseModalVisible.value = false;-->
<!--    courseListRef.value?.loadData();-->
<!--  };-->

<!--  const queryParams = ref<any>({-->
<!--    category: undefined,-->
<!--    grade: undefined,-->
<!--    period: undefined,-->
<!--  });-->
<!--</script>-->

<!--<template>-->
<!--  <a-card title="数字资源 - 课程列表">-->
<!--    <template #extra>-->
<!--      <a-space>-->
<!--        <a-button size="mini" type="primary" @click="() => handleShowEditCourse(undefined)">-->
<!--          <template #icon>-->
<!--            <IconPlus />-->
<!--          </template>-->
<!--          新建课程-->
<!--        </a-button>-->

<!--        <a-button size="mini" type="outline" @click="() => router.push('/resource/digital/authorization')">-->
<!--          <template #icon>-->
<!--            <IconCheck />-->
<!--          </template>-->
<!--          课程资源授权-->
<!--        </a-button>-->
<!--      </a-space>-->
<!--    </template>-->

<!--    <div class="flex-container gap-10">-->
<!--      <div class="filters">-->
<!--        <h2 class="title">-->
<!--          <IconFilter />-->
<!--          课程分类-->
<!--        </h2>-->
<!--        <ul class="items">-->
<!--          <li :class="{ active: !queryParams.category }" @click="() => (queryParams.category = undefined)">全部 </li>-->
<!--          <li-->
<!--            v-for="category in COURSE_CATEGORIES"-->
<!--            :key="category"-->
<!--            :class="{ active: queryParams.category === category }"-->
<!--            @click="() => (queryParams.category = category)"-->
<!--          >-->
<!--            {{ category }}-->
<!--          </li>-->
<!--        </ul>-->
<!--        <h2 class="title">-->
<!--          <IconFilter />-->
<!--          年级-->
<!--        </h2>-->
<!--        <ul class="items">-->
<!--          <li :class="{ active: !queryParams.grade }" @click="() => (queryParams.grade = undefined)">全部 </li>-->
<!--          <li-->
<!--            v-for="grade in COURSE_GRADES"-->
<!--            :key="grade"-->
<!--            :class="{ active: queryParams.grade === grade }"-->
<!--            @click="() => (queryParams.grade = grade)"-->
<!--          >-->
<!--            {{ grade }}-->
<!--          </li>-->
<!--        </ul>-->
<!--        <h2 class="title">-->
<!--          <IconFilter />-->
<!--          上下册-->
<!--        </h2>-->
<!--        <ul class="items">-->
<!--          <li :class="{ active: !queryParams.period }" @click="() => (queryParams.period = undefined)">全部 </li>-->
<!--          <li-->
<!--            v-for="period in COURSE_PERIODS"-->
<!--            :key="period"-->
<!--            :class="{ active: queryParams.period === period }"-->
<!--            @click="() => (queryParams.period = period)"-->
<!--          >-->
<!--            {{ period }}-->
<!--          </li>-->
<!--        </ul>-->
<!--      </div>-->
<!--      <course-list-->
<!--        ref="courseListRef"-->
<!--        class="flex-1"-->
<!--        api-alias="/resource/course"-->
<!--        :query-params="queryParams"-->
<!--        @edit-course="handleShowEditCourse"-->
<!--      >-->
<!--        <template #default-actions="{ record }">-->
<!--          <a-button-->
<!--            size="mini"-->
<!--            type="primary"-->
<!--            @click="-->
<!--              () =>-->
<!--                router.push({-->
<!--                  path: '/resource/digital/management',-->
<!--                  query: {-->
<!--                    courseId: record.id,-->
<!--                  },-->
<!--                })-->
<!--            "-->
<!--            >资源管理-->
<!--          </a-button>-->
<!--        </template>-->
<!--      </course-list>-->
<!--    </div>-->

<!--    <create-course-modal-->
<!--      v-if="createCourseModalVisible"-->
<!--      v-model="createCourseModalVisible"-->
<!--      :course="currentRow"-->
<!--      api-alias="/resource/course"-->
<!--      @on-ok="handleCreateCourseOk"-->
<!--    />-->
<!--  </a-card>-->
<!--</template>-->

<!--<style lang="less" scoped>-->
<!--  .flex-container.gap-10 {-->
<!--    align-items: flex-start;-->
<!--  }-->

<!--  .filters {-->
<!--    border-right: 1px solid #ddd;-->
<!--    width: 140px;-->
<!--    text-align: right;-->
<!--    padding-right: 10px;-->
<!--    .title {-->
<!--      font-size: 14px;-->
<!--      font-weight: normal;-->
<!--      color: #000;-->
<!--      margin-bottom: 10px;-->
<!--      display: flex;-->
<!--      justify-content: center;-->
<!--      align-items: center;-->
<!--      .arco-icon {-->
<!--        margin-right: 5px;-->
<!--      }-->
<!--    }-->
<!--    .items {-->
<!--      list-style: none;-->
<!--      padding: 0;-->
<!--      margin: 0 0 20px;-->
<!--      line-height: 140%;-->
<!--      color: #777;-->
<!--      display: flex;-->
<!--      flex-wrap: wrap;-->
<!--      text-align: center;-->
<!--      li {-->
<!--        flex: 0 0 50%;-->
<!--        margin-bottom: 5px;-->
<!--        cursor: pointer;-->
<!--        font-size: 12px;-->

<!--        &:hover,-->
<!--        &.active {-->
<!--          color: rgb(var(&#45;&#45;primary-6));-->
<!--        }-->

<!--        &.active {-->
<!--          font-weight: bold;-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--  }-->
<!--</style>-->

<template>
  <div></div>
</template>

<script setup lang="ts"></script>
