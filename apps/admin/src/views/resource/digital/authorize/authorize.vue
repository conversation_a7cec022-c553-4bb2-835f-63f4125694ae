<script setup lang="ts">
  import { onMounted, provide, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import openapi from '@repo/infrastructure/openapi';
  import SelectOrg from '@/views/resource/digital/authorize/step/selectOrg.vue';
  import SelectResource from '@/views/resource/digital/authorize/step/selectResource.vue';

  const org = ref<any>();
  const route = useRoute();
  const router = useRouter();
  const currentStep = ref<any>(1);

  const handleOrgSelect = (selected: any) => {
    org.value = selected;
    currentStep.value = 2;
  };

  onMounted(async () => {
    if (route.query.id) {
      const { data } = await openapi.org.orgQuery({
        id: Number(route.query.id),
      });
      org.value = data;
    }

    if (org.value) {
      currentStep.value = 2;
    }
  });

  provide('org', org);
  provide('currentStep', currentStep);
</script>

<template>
  <a-card>
    <template #title>
      <div class="flex items-center">
        <div class="flex-1">课程资源授权</div>
        <a-button type="primary" size="mini" status="success" @click="router.back()">
          <template #icon>
            <IconCheck />
          </template>
          完成
        </a-button>
      </div>
    </template>
    <div class="py-8 w-96 mx-auto">
      <a-steps :current="currentStep" label-placement="vertical">
        <a-step title="选择授权单位">
          <template #description>
            <div v-if="org?.id" class="text-green-700 font-bold">
              {{ org?.name }}
            </div>
          </template>
        </a-step>
        <a-step title="选择资源" description="稍后仍可继续添加" />
        <!--        <a-step title="授权完成" />-->
      </a-steps>
    </div>
    <select-org v-if="currentStep === 1" @org-select="handleOrgSelect" />
    <select-resource v-if="currentStep === 2" />
  </a-card>
</template>

<style scoped lang="less"></style>
