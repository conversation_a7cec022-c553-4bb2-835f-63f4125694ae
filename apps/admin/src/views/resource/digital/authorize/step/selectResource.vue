<!--<script setup lang="ts">-->
<!--  import { COURSE_CATEGORIES, COURSE_GRADES, COURSE_PERIODS } from '@repo/infrastructure/constants';-->
<!--  import { resourceType } from '@repo/components/course/constants';-->
<!--  import { inject, onMounted, ref } from 'vue';-->
<!--  import openapi from '@repo/infrastructure/openapi';-->
<!--  import dayjs from 'dayjs';-->
<!--  import { useLoading } from '@repo/infrastructure/hooks';-->
<!--  import { Message, Modal } from '@arco-design/web-vue';-->
<!--  import CourseList from '@/views/course/components/courseList.vue';-->

<!--  const org: any = inject('org');-->
<!--  const currentCourse = ref<any>(null);-->
<!--  const linkAuthorizeVisible = ref<any>(false);-->
<!--  const now = new Date();-->
<!--  const expiredAt = ref<any>(dayjs(now).add(1, 'year').toDate());-->
<!--  const { loading, setLoading } = useLoading();-->

<!--  const queryParams = ref<any>({-->
<!--    category: '',-->
<!--    grade: '',-->
<!--    period: '',-->
<!--    selectedChapter: '',-->
<!--    type: '',-->
<!--  });-->

<!--  const authorizedCourseList = ref<any[]>([]);-->
<!--  const authorizedCourseIds = ref<Record<number, any>>({});-->

<!--  const loadAuthorized = async () => {-->
<!--    const { data } = await openapi.resource.getDigitalResourceAuthorizedCourse({-->
<!--      orgId: org.value.id,-->
<!--    });-->

<!--    authorizedCourseIds.value = {};-->
<!--    authorizedCourseList.value = data || [];-->
<!--    authorizedCourseList.value.forEach((item: any) => {-->
<!--      authorizedCourseIds.value[item.resourceCourseId] = {-->
<!--        authorizeId: item.id,-->
<!--        courseId: item.resourceCourseId,-->
<!--        isCopy: item.isCopy,-->
<!--        expiredAt: !item.isCopy ? dayjs(item.expiredAt).format('YYYY-MM-DD') : '',-->
<!--      };-->
<!--    });-->
<!--  };-->

<!--  const handleAuthorize = async (courseId: number, isCopy: boolean) => {-->
<!--    const reqFunc = async () => {-->
<!--      setLoading(true);-->
<!--      try {-->
<!--        await openapi.resource.authorizeCourseResourceTo({-->
<!--          toOrgId: org.value.id,-->
<!--          courseIds: [courseId],-->
<!--          isCopy,-->
<!--          expiredAt: (!isCopy ? expiredAt.value : undefined) as any,-->
<!--        });-->

<!--        await loadAuthorized();-->

<!--        Message.success('授权成功');-->
<!--      } finally {-->
<!--        setLoading(false);-->
<!--      }-->
<!--    };-->
<!--    if (isCopy) {-->
<!--      Modal.confirm({-->
<!--        title: '复制资源',-->
<!--        content: '确定要授权使用本资源副本吗？注意本操作不可撤销！',-->
<!--        okButtonProps: {-->
<!--          status: 'danger',-->
<!--        },-->
<!--        onOk: reqFunc,-->
<!--      });-->
<!--    } else {-->
<!--      await reqFunc();-->
<!--    }-->
<!--  };-->

<!--  const handelShowLinkAuthorize = (record: any) => {-->
<!--    currentCourse.value = record;-->
<!--    linkAuthorizeVisible.value = true;-->
<!--  };-->

<!--  const handleHideLinkAuthorize = () => {-->
<!--    linkAuthorizeVisible.value = false;-->
<!--  };-->

<!--  const handleCancelAuthorize = async (id: number) => {-->
<!--    Modal.confirm({-->
<!--      title: '取消授权',-->
<!--      content: '确定要取消这个资源课程的使用授权吗？',-->
<!--      onOk: async () => {-->
<!--        setLoading(true);-->
<!--        try {-->
<!--          await openapi.resource.removeDigitalResourceCourseAuthorization({-->
<!--            id,-->
<!--          });-->
<!--          await loadAuthorized();-->
<!--          Message.success('取消授权成功');-->
<!--        } finally {-->
<!--          setLoading(false);-->
<!--        }-->
<!--      },-->
<!--    });-->
<!--  };-->

<!--  onMounted(async () => {-->
<!--    await loadAuthorized();-->
<!--  });-->
<!--</script>-->

<!--<template>-->
<!--  <a-spin :loading="loading" class="w-full">-->
<!--    <div class="flex items-center justify-center gap-4">-->
<!--      <a-select v-model="queryParams.grade" size="mini" placeholder="选择年级" allow-clear class="w-48">-->
<!--        <a-option v-for="g in COURSE_GRADES" :key="g" :value="g" :label="g" />-->
<!--      </a-select>-->
<!--      <a-select v-model="queryParams.period" size="mini" placeholder="上下册" allow-clear class="w-48">-->
<!--        <a-option v-for="g in COURSE_PERIODS" :key="g" :value="g" :label="g" />-->
<!--      </a-select>-->
<!--      <a-select v-model="queryParams.category" size="mini" placeholder="课程分类" allow-clear class="w-48">-->
<!--        <a-option v-for="g in COURSE_CATEGORIES" :key="g" :value="g" :label="g" />-->
<!--      </a-select>-->
<!--      <a-select v-model="queryParams.type" size="mini" placeholder="资源类型" allow-clear class="w-48">-->
<!--        <a-option value="all">全部资源</a-option>-->
<!--        <a-option v-for="t in resourceType" :key="t.id" :value="t.id">-->
<!--          {{ t.name }}-->
<!--        </a-option>-->
<!--      </a-select>-->
<!--    </div>-->

<!--    <div class="my-8 mx-32">-->
<!--      <course-list-->
<!--        ref="courseListRef"-->
<!--        class="flex-1 w-full"-->
<!--        api-alias="/resource/course"-->
<!--        :query-params="queryParams"-->
<!--        empty-description="暂无相关课程"-->
<!--        :page-size="999"-->
<!--      >-->
<!--        <template #buttons="{ record }">-->
<!--          <div v-if="authorizedCourseIds[record.id]">-->
<!--            <div v-if="!authorizedCourseIds[record.id].isCopy" class="flex flex-col gap-3">-->
<!--              <a-button size="mini" type="primary" disabled-->
<!--                >至：{{ authorizedCourseIds[record.id].expiredAt }}</a-button-->
<!--              >-->
<!--              <a-button-->
<!--                size="mini"-->
<!--                type="primary"-->
<!--                status="danger"-->
<!--                class="w-full"-->
<!--                @click="() => handleCancelAuthorize(authorizedCourseIds[record.id].authorizeId)"-->
<!--                >取消授权</a-button-->
<!--              >-->
<!--            </div>-->
<!--            <div v-else class="p-2 text-center bg-slate-600 text-white text-sm">-->
<!--              <div>已授权复制使用</div>-->
<!--            </div>-->
<!--          </div>-->
<!--          <div v-else class="flex flex-col gap-3">-->
<!--            <a-button size="mini" type="primary" @click="() => handelShowLinkAuthorize(record)">授权使用</a-button>-->
<!--            <a-button-->
<!--              :title="`确定要授权使用本资源副本吗？`"-->
<!--              size="mini"-->
<!--              type="primary"-->
<!--              @click="() => handleAuthorize(record.id, true)"-->
<!--              >复制使用</a-button-->
<!--            >-->
<!--          </div>-->
<!--        </template>-->

<!--        <template #extra="{ record }">-->
<!--          <div-->
<!--            v-if="authorizedCourseIds[record.id]?.isCopy"-->
<!--            class="text-sm bg-amber-700 text-white absolute top-0 right-0 px-2 py-1"-->
<!--          >-->
<!--            <IconPaste />-->
<!--            已复制-->
<!--          </div>-->
<!--          <div-->
<!--            v-if="authorizedCourseIds[record.id] && !authorizedCourseIds[record.id].isCopy"-->
<!--            class="text-sm bg-green-800 text-white absolute top-0 right-0 px-2 py-1"-->
<!--          >-->
<!--            <IconCheck />-->
<!--            已授权-->
<!--          </div>-->
<!--        </template>-->
<!--      </course-list>-->
<!--    </div>-->

<!--    <a-modal-->
<!--      v-model:visible="linkAuthorizeVisible"-->
<!--      title="选择授权期限"-->
<!--      @cancel="handleHideLinkAuthorize"-->
<!--      @ok="() => handleAuthorize(currentCourse.id, false)"-->
<!--    >-->
<!--      <div class="flex items-center justify-center flex-col gap-4">-->
<!--        <div>-->
<!--          授权使用-->
<!--          <strong>-->
<!--            {{ currentCourse?.grade }}-->
<!--            {{ currentCourse?.period }}-->
<!--            {{ currentCourse?.category }}-->
<!--          </strong>-->
<!--          至：-->
<!--        </div>-->
<!--        <div>-->
<!--          <a-date-picker v-model="expiredAt" size="mini" type="date" placeholder="选择授权截止日期" />-->
<!--        </div>-->
<!--      </div>-->
<!--    </a-modal>-->
<!--  </a-spin>-->
<!--</template>-->

<!--<style scoped lang="less"></style>-->

<template>
  <div></div>
</template>

<script setup lang="ts"></script>
