<script setup lang="ts">
  import DefaultLayout from '@/common/layout/default-layout.vue';
  import { useUserStore } from '@repo/infrastructure/store';
  import { useRouter } from 'vue-router';
  import { onMounted, ref } from 'vue';
  import NavCenterTabs from '@/common/layout/components/navCenterTabs.vue';

  const userStore = useUserStore();

  const tabs = [
    { key: 'visit', title: '来访登记', visible: userStore.isAuthorized('admin_org:visit') },
    { key: 'application', title: '咨询服务', visible: userStore.isAuthorized('admin_org:application:manage') },
    { key: 'docTemplate', title: '文档模板', visible: userStore.isAuthorized('admin_org:docTemplate') },
    { key: 'supervision', title: '常规巡检', visible: userStore.isAuthorized('admin_org:supervision:manage') },
    { key: 'conference', title: '会议培训管理', visible: userStore.isAuthorized('admin_org:workRecord:conference') },
    { key: 'event', title: '中心大事记', visible: userStore.isAuthorized('admin_org:workRecord:event') },
    { key: 'weapp', title: '小程序管理', visible: userStore.isAuthorized('admin_org:workRecord:weapp') },
    { key: 'asset', title: '资产管理', visible: userStore.isAuthorized('admin_org:workRecord:weapp') },
    { key: 'paperCompetition', title: '论文赛课', visible: true },
  ].filter((tab) => tab.visible);

  const router = useRouter();
  const activeTab = ref<any>('');

  const handleTabsChange = (key: string) => {
    router.push(`/centerManage/${key}`);
    activeTab.value = key;
  };

  onMounted(() => {
    const splited = router.currentRoute.value.path.split('/');
    if (splited.length < 3) {
      const currentTab = splited[2];
      if (currentTab) {
        handleTabsChange(currentTab);
      } else {
        handleTabsChange(tabs.filter((tab) => tab.visible)[0].key);
      }
    }
  });
</script>

<template>
  <default-layout>
    <template #page-title>中心管理</template>
    <template #navbar-center>
      <nav-center-tabs :active="activeTab" :tabs="tabs" @change="handleTabsChange" />
    </template>
    <div class="main-wrapper">
      <router-view />
    </div>
  </default-layout>
</template>

<style scoped lang="less">
  .main-wrapper {
    min-height: calc(100vh - 60px);
  }
</style>
