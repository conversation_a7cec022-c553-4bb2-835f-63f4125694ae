<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable, TableAction } from '@repo/ui/components/table';

  const schema = ref();
  const tableRef = ref<any>(null);

  const columns = ['assetInfo', 'source', 'beforeQuantity', 'quantity', 'afterQuantity', 'createdDate', 'operator'];

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/asset/assetTransferLog');
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">资产变动记录</div>
        <table-action
          v-if="tableRef"
          :schema="schema"
          :table="tableRef"
          component-size="mini"
          :visible-components="['refresh', 'quickSearch']"
        />
      </div>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :show-row-actions="false"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
    >
      <!--      <template #custom-column-inventory="{ record }"> {{ record.inventory }} {{ record.unit }} </template>-->
      <!--      <template #custom-column-availableInventory="{ record }">-->
      <!--        {{ record.availableInventory }} {{ record.unit }}-->
      <!--      </template>-->
    </crud-table>
  </a-card>
</template>

<style scoped lang="scss"></style>
