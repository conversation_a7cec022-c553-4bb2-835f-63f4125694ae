<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { CrudTable, TableAction } from '@repo/ui/components/table';
  import { ModalForm } from '@repo/ui/components/form';
  import AssetInventoryAdjust from '@/views/centerManage/asset/components/assetInventoryAdjust.vue';

  const editVisible = ref(false);
  const inventoryAdjustVisible = ref(false);
  const currentEditRow = ref<any>({});
  const schema = ref<any>(null);
  const tableRef = ref<any>(null);

  const handleShowEdit = (raw?: any) => {
    editVisible.value = true;
    currentEditRow.value = raw || {};
  };

  const columns = ['propertyType', 'symbol', 'name', 'inventory', 'availableInventory', 'tag', 'createdDate'];

  const handleRowAction = (action: any, record?: any) => {
    if (action.key === 'edit' || action.key === 'add') {
      handleShowEdit(record || {});
    } else if (action.key === 'adjustInventory') {
      inventoryAdjustVisible.value = true;
      currentEditRow.value = record;
    }
  };

  onMounted(async () => {
    schema.value = await SchemaHelper.getInstanceByDs('/asset/assetInfo');
  });

  defineExpose({
    loadData: async (params?: any) => {
      await tableRef.value.loadData(params);
    },
    handleShowEdit,
  });
</script>

<template>
  <a-card v-if="schema">
    <template #title>
      <div class="flex justify-between">
        <div class="flex-1">资产基本信息管理</div>
        <table-action
          v-if="tableRef"
          :schema="schema"
          :table="tableRef"
          component-size="mini"
          @row-action="handleRowAction"
        />
      </div>
    </template>
    <crud-table
      ref="tableRef"
      :schema="schema"
      class="mt-2"
      :default-query-params="{ sort: '-id' }"
      :visible-columns="columns"
      @row-action="handleRowAction"
    >
      <template #custom-column-name="{ record }">
        <a-popover v-if="record.coverImage" title="资产图片">
          <template #content><img :src="record.coverImage" style="max-width: 600px; max-height: 600px" /></template>
          <div class="text-blue-600 cursor-pointer"> {{ record.name }} </div>
        </a-popover>
        <span v-else>{{ record.name }}</span>
      </template>
      <template #custom-column-inventory="{ record }"> {{ record.inventory }} {{ record.unit }} </template>
      <template #custom-column-availableInventory="{ record }">
        {{ record.availableInventory }} {{ record.unit }}
      </template>
    </crud-table>
    <modal-form
      v-model:visible="editVisible"
      v-model="currentEditRow"
      :schema="schema"
      modal-name="资产信息"
      @ok="() => tableRef.loadData({})"
    />
    <asset-inventory-adjust
      v-model="currentEditRow"
      v-model:visible="inventoryAdjustVisible"
      @ok="() => tableRef.loadData({})"
    />
  </a-card>
</template>

<style scoped lang="scss"></style>
