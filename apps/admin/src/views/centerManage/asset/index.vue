<script setup lang="ts">
  import { ref } from 'vue';
  import AssetInfo from '@/views/centerManage/asset/assetInfo.vue';
  import TransferLog from '@/views/centerManage/asset/transferLog.vue';
  import AssetBorrow from '@/views/centerManage/asset/assetBorrow.vue';

  const activeKey = ref('assetInfo');
</script>

<template>
  <a-card class="m-2 h-full">
    <a-tabs v-model:active-key="activeKey">
      <a-tab-pane key="assetInfo" title="资产信息管理">
        <asset-info v-if="activeKey === 'assetInfo'" />
      </a-tab-pane>
      <a-tab-pane key="application" title="资产借用登记">
        <asset-borrow v-if="activeKey === 'application'" />
      </a-tab-pane>
      <a-tab-pane key="transferLog" title="资产可用库存变动情况">
        <transfer-log v-if="activeKey === 'transferLog'" />
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<style scoped lang="scss"></style>
