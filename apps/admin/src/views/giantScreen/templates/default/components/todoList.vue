<template>
  <vue3ScrollSeamless v-if="dataList?.length" :class-option="optionSingleHeight" :data="dataList" class="seamless-warp">
    <ul class="item">
      <li v-for="(item, index) in dataList" :key="index">
        <span class="date">
          {{ dayjs(item.date).format('YYYY年MM月DD日') }}
        </span>
        <span class="subject">
          {{ item.remark }}
        </span>
      </li>
    </ul>
  </vue3ScrollSeamless>
</template>

<script setup lang="ts">
  import { vue3ScrollSeamless } from 'vue3-scroll-seamless';
  import dayjs from 'dayjs';
  import { PropType, ref } from 'vue';

  const props = defineProps({
    rawData: {
      type: Array as PropType<any[]>,
    },
  });

  const dataList = ref([...(props.rawData || [])]);

  const optionSingleHeight = {
    singleHeight: 45,
    waitTime: 3000,
    hoverStop: true,
  };
</script>

<style lang="scss" scoped>
  .seamless-warp {
    height: 268px;
    margin-top: 10px;
    overflow: hidden !important;
  }
  ul,
  li {
    padding: 0;
    margin: 0;
  }
  li {
    height: 45px;
    line-height: 45px;
    border-bottom: 1px solid #1c6ca1;
    list-style: none;
    margin: 0 10px;
    font-size: 18px;
    display: flex;
    justify-content: space-around;
    text-align: center;
    > * {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .date {
      width: 130px;
    }
    .subject {
      text-align: left;
      flex: 1;
    }
  }
</style>
