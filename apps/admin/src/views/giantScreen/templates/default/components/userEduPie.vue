<template>
  <div id="userEduPie"></div>
</template>

<script>
  import { Pie } from '@antv/g2plot';

  export default {
    name: 'UserEduPie',
    props: {
      rawData: {
        type: Array,
      },
    },
    mounted() {
      const piePlot = new Pie('userEduPie', {
        appendPadding: 40,
        radius: 1,
        data: this.rawData,
        angleField: 'num',
        colorField: 'label',
        width: 280,
        height: 280,
        theme: 'giant-screen',
        label: {
          type: 'spider',
          formatter: (item) => {
            return `${item.label}${item.num}人`;
          },
          style: {
            fontSize: 12,
            color: '#ffffff',
          },
        },
        legend: {
          visible: true,
          title: {
            style: {
              color: '#ffffff',
            },
          },
        },
        statistic: {
          title: false,
          content: false,
        },
      });
      piePlot.render();
    },
  };
</script>
