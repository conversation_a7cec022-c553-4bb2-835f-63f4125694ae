<template>
  <div id="fusionSchoolGiantBar" style="height: 100%; width: 100%"></div>
</template>

<script setup lang="ts">
  import { Column } from '@antv/g2plot';
  import { nextTick, onMounted, ref } from 'vue';

  const props = defineProps({
    data: {
      type: Array,
    },
  });

  const typesMap = {};
  const plot = ref(null);

  onMounted(() => {
    props.data?.forEach((item) => {
      if (typesMap[item.type]) {
        typesMap[item.type] += 1;
      } else {
        typesMap[item.type] = 1;
      }
    });

    const data = Object.keys(typesMap).map((key) => {
      return {
        type: key,
        count: typesMap[key],
      };
    });

    const indexes = ['特殊教育学校', '一贯制', '幼儿园', '小学', '初中', '高中', '中职', '高职', '教学点', '其他'];
    data.sort((a, b) => {
      return indexes.indexOf(a.type) - indexes.indexOf(b.type);
    });

    plot.value = new Column('fusionSchoolGiantBar', {
      data,
      color: 'rgb(13, 224, 157)',
      xField: 'type',
      yField: 'count',
      theme: 'giant-screen',
      legend: {
        show: true,
        position: 'top',
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: true,
          size: 50,
        },
      },
      smooth: true,
      meta: {
        type: {
          alias: '类型',
        },
        count: {
          alias: '数量',
        },
      },
    });

    nextTick(() => {
      plot.value.render();
    });
  });
</script>
