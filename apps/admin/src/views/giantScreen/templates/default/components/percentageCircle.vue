<template>
  <div :id="container"></div>
</template>

<script setup lang="ts">
  import { RingProgress } from '@antv/g2plot';
  import { computed, onMounted } from 'vue';

  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    label: {
      type: String,
      required: true,
    },
    max: {
      type: Number,
      required: true,
    },
    value: {
      type: Number,
      required: true,
    },
    unit: {
      type: String,
    },
    activeColor: {
      type: String,
      default: '#00b4d8',
    },
    inactiveColor: {
      type: String,
      default: '#ebedf0',
    },
    size: {
      type: Number,
      default: 100,
    },
  });

  const emit = defineEmits(['update:value']);

  const percentage = computed(() => {
    return (props.value / props.max) * 100;
  });

  const container = computed(() => {
    return `percentageCircle-${props.id}`;
  });

  onMounted(() => {
    const ringProgress = new RingProgress(container.value, {
      height: props.size,
      width: props.size,
      autoFit: false,
      theme: 'giant-screen',
      percent: percentage.value / 100,
      color: [props.activeColor, props.inactiveColor],
      innerRadius: 0.85,
      radius: 0.98,
      statistic: {
        title: {
          style: { color: '#fff', fontSize: '18px', lineHeight: '18px' },
          formatter: () => props.label,
        },
        content: {
          style: {
            color: props.activeColor,
            fontSize: '27px',
            lineHeight: '22px',
            marginTop: '8px',
            fontWeight: 500,
          },
          formatter: () => {
            return `${props.value || 0}${props.unit || ''}`;
          },
        },
      },
    });

    ringProgress.render();
  });
</script>
