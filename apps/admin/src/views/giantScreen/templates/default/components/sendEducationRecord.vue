<template>
  <vue3ScrollSeamless :class-option="optionSingleHeight" :data="rawData" class="seamless-warp">
    <ul class="item">
      <li v-for="(item, index) in rawData" :key="index">
        <div class="date">
          {{ dayjs(item.date).format('YYYY年MM月DD日') }}
        </div>
        <div class="school">
          <div class="ov">{{ item.udf1 }}</div>
        </div>
        <div class="student">
          {{ item.remark }}
        </div>
      </li>
    </ul>
  </vue3ScrollSeamless>
</template>

<script>
  import { vue3ScrollSeamless } from 'vue3-scroll-seamless';
  import dayjs from 'dayjs';

  export default {
    name: 'SendEducationRecord',
    components: {
      vue3ScrollSeamless,
    },
    props: {
      rawData: {
        type: Array,
      },
    },
    computed: {
      dayjs() {
        return dayjs;
      },
      optionSingleHeight() {
        return {
          singleHeight: 40,
          waitTime: 3000,
        };
      },
    },
  };
</script>

<style lang="scss" scoped>
  .seamless-warp {
    height: 122px;
    margin-top: 10px;
    overflow: hidden !important;
  }
  ul,
  li {
    padding: 0;
    margin: 0;
  }
  li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #1c6ca1;
    list-style: none;
    margin: 0 10px;
    font-size: 18px;
    display: flex;
    justify-content: space-around;
    text-align: center;
    .date {
      width: 150px;
    }
    .school {
      flex: 1;
      overflow: hidden;
      .ov {
        display: block;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .student {
      width: 120px;
      font-weight: bold;
      color: rgb(225, 178, 31);
    }
  }
</style>
