<template>
  <div id="studentAgeBar"></div>
</template>

<script>
  import { Column } from '@antv/g2plot';

  export default {
    name: 'StudentAgeBar',
    props: {
      rawData: {
        type: Array,
      },
    },
    mounted() {
      const indexes = ['6岁及以下', '7-12岁', '13-18岁', '18岁以上'];
      const data = [...(this.rawData || [])].sort((a, b) => {
        return indexes.indexOf(a.remark) - indexes.indexOf(b.remark);
      });
      const stackedColumnPlot = new Column('studentAgeBar', {
        data,
        isGroup: true,
        xField: 'remark',
        yField: 'id',
        height: 320,
        appendPadding: 16,
        seriesField: 'udf1',
        theme: 'giant-screen',
        /** 设置颜色 */
        // color: ['#1ca9e6', '#f88c24'],
        /** 设置间距 */
        // marginRatio: 0.1,
        label: {
          content(item) {
            return `${item.id}`;
          },
          // 可手动配置 label 数据标签位置
          position: 'top', // 'top', 'middle', 'bottom'
          // 可配置附加的布局方法
          layout: [
            // 柱形图数据标签位置自动调整
            // { type: 'interval-adjust-position' },
            // 数据标签防遮挡
            { type: 'interval-hide-overlap' },
            // 数据标签文颜色自动调整
            // { type: 'adjust-color' },
          ],
        },
      });

      stackedColumnPlot.render();
    },
  };
</script>
