<template>
  <div>
    <a-table size="small" :data="list" border :pagination="false">
      <template #columns>
        <a-table-column data-index="createdDate" title="日期" width="150">
          <template #cell="scope">
            {{ dayjs(scope.record.createdDate).format('YYYY年MM月DD日') }}
          </template>
        </a-table-column>
        <a-table-column data-index="subject" title="标题">
          <template #cell="scope">
            <a-link type="primary" :underline="false" @click="() => handleShowDetail(scope.record)">{{
              scope.record.subject
            }}</a-link>
          </template>
        </a-table-column>
      </template>
    </a-table>

    <a-modal v-model:visible="visible" :title="currentRow?.subject || '工作记录'" width="80%" simple hide-cancel>
      <div v-if="currentRow" class="wrapper">
        <div class="title">{{ currentRow.subject }}</div>
        <div class="content article-content" v-html="currentRow.content"></div>
      </div>
      <template #footer>
        <div class="dialog-footer text-center mt-2">
          <a-button size="small" type="primary" plain @click="handleHide">关 闭</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { PROJECT_URLS } from '@repo/env-config';
  import dayjs from 'dayjs';
  import { computed, onMounted, ref } from 'vue';
  import { request } from '@repo/infrastructure/request';

  const emit = defineEmits(['loading']);
  const list = ref([]);
  const currentRow = ref(null);

  const visible = ref(false);

  const loadList = async () => {
    emit('loading', true);
    const { data: res } = await request('/resourceCenter/conference', {
      params: {
        page: 1,
        pageSize: 50,
      },
      baseURL: PROJECT_URLS.MAIN_PROJECT_API,
    });
    list.value = res.items;
    emit('loading', false);
  };

  const handleShowDetail = (row) => {
    currentRow.value = row;
    visible.value = true;
  };

  const handleHide = () => {
    visible.value = false;
  };

  onMounted(loadList);
</script>

<style lang="scss" scoped>
  .wrapper {
    padding: 16px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
  }
  .title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    margin-top: 6px;
  }
  .content {
    font-size: 16px;
    line-height: 1.5;
    text-indent: 2em;
  }
</style>

<style>
  .article-content img {
    display: block;
    max-width: 100%;
    margin: 5px 0;
  }
</style>
