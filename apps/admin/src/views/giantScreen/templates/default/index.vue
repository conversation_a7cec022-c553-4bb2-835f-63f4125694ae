<template>
  <div v-if="ready">
    <div class="header">
      <div class="time">
        {{ currentTime }}
      </div>
      <div class="title" :style="rawData.companyName?.length < 10 ? 'letter-spacing: 10px' : ''">
        {{ rawData.companyName }}
      </div>
    </div>

    <div class="main-wrap is-flex">
      <div class="left ml">
        <div class="is-flex first-block">
          <div class="school mini-card clickable" @click="() => showListModal('fusionSchool')">
            <div class="title">融合学校</div>
            <div class="num">{{ rawData.school?.length }}所</div>
          </div>
          <div class="room mini-card ml clickable" @click="() => showListModal('resourceRoom')">
            <div class="title">资源教室</div>
            <div class="num"> {{ rawData.school?.filter((item) => item.hasResourceRoom).length }}个 </div>
          </div>
        </div>

        <div class="card-view mt" style="height: 250px">
          <div class="title">融合学校</div>
          <div class="content" style="padding: 20px">
            <fusion-school-bar :data="rawData.school" />
          </div>
        </div>
        <div v-show="false" class="card-view mt" style="height: 250px">
          <div class="title">教康资源</div>
          <div class="content" style="padding: 20px">
            <resource-count-bytype :raw-data="rawData.resourceCountByType" />
          </div>
        </div>
        <div class="card-view mt clickable" style="flex: 1" @click="() => showListModal('conference')">
          <div class="title">工作记录</div>
          <div class="content">
            <todo-list :raw-data="rawData.conferenceList" />
          </div>
        </div>
      </div>
      <div class="middle">
        <div class="first-block is-flex">
          <div class="card-view" style="height: 370px; flex: 1">
            <div class="title">年龄段占比</div>
            <div class="content" style="margin: 8px 16px">
              <student-age-bar :raw-data="rawData.studentAges" />
            </div>
          </div>
          <div class="first-right ml" style="height: 370px">
            <div
              class="mini-card clickable"
              style="height: 188px; line-height: 70px"
              @click="() => showListModal('allStudent')"
            >
              <div style="padding-top: 30px">
                <div class="title">学生总数</div>
                <div class="num">{{ rawData.studentCount }}人</div>
              </div>
            </div>
            <div class="mini-card clickable" style="height: 110px" @click="() => showListModal('specialStudent')">
              <div class="title">特教学校学生</div>
              <div class="num">{{ rawData.specialStudentCount }}人</div>
            </div>
            <div class="mini-card clickable" style="height: 110px" @click="() => showListModal('normalStudent')">
              <div class="title">普通学校学生</div>
              <div class="num">{{ rawData.normalStudentCount }}人</div>
            </div>
            <div class="mini-card clickable" style="height: 110px" @click="() => showListModal('sendStudent')">
              <div class="title">送教上门学生</div>
              <div class="num">{{ rawData.sendStudentCount }}人</div>
            </div>
          </div>
        </div>
        <div class="second-block is-flex mt">
          <div class="left-item is-flex space-around" style="width: 54%">
            <percentage-circle
              id="student-male"
              inactive-color="#0b528e"
              active-color="#25a6ff"
              :size="128"
              :value="rawData.maleStudentCount"
              :max="rawData.maleStudentCount + rawData.femaleStudentCount"
              label="男生"
            />

            <percentage-circle
              id="student-female"
              inactive-color="#0b528e"
              active-color="#fe5c5c"
              :size="128"
              :value="rawData.femaleStudentCount"
              :max="rawData.maleStudentCount + rawData.femaleStudentCount"
              label="女生"
            />
          </div>
          <div v-show="false" class="card-view ml clickable" @click="() => showListModal('sendRecord')">
            <div class="title">送教上门记录</div>
            <div class="content">
              <send-education-record :raw-data="rawData.sendEducationRecordList" />
            </div>
          </div>
        </div>
        <div class="card-view mt" style="flex: 1">
          <div class="title">障碍类型分布情况</div>
          <div class="content">
            <student-disorders-count :raw-data="rawData.disorders" />
          </div>
        </div>
      </div>
      <div class="right mr">
        <div class="is-flex first-block">
          <div class="school mini-card clickable" @click="() => showListModal('specialTeacher')">
            <div class="title">特校教师</div>
            <div class="num">{{ rawData.specialTeacherCount }}人</div>
          </div>
          <div class="room mini-card ml clickable" @click="() => showListModal('normalTeacher')">
            <div class="title">资源教师</div>
            <div class="num">{{ rawData.resourceTeacherCount }}人</div>
          </div>
        </div>
        <div class="card-view mt" style="height: 320px">
          <div class="title">师资情况</div>
          <div class="content">
            <user-edu-pie :raw-data="rawData.userEducation" />
          </div>
        </div>
        <div class="card-view mt" style="flex: 1">
          <div class="title">职称分布</div>
          <div class="content">
            <user-title-bar :raw-data="rawData.userTitle" />
          </div>
        </div>
      </div>
    </div>

    <list-modal
      v-model:modal-config="listModalOptions"
      :title="listModalOptions.title"
      :raw-data="rawData"
      :value="listModalOptions.visible"
      :width="listModalOptions.width || '80%'"
      @update:model-value="handleListModalVisible"
    />
  </div>
</template>

<script lang="ts" setup>
  import FusionSchoolBar from '@/views/giantScreen/templates/default/components/fusionSchoolBar.vue';
  import ResourceCountBytype from '@/views/giantScreen/templates/default/components/resourceCountBytype.vue';
  import PercentageCircle from '@/views/giantScreen/templates/default/components/percentageCircle.vue';
  import SendEducationRecord from '@/views/giantScreen/templates/default/components/sendEducationRecord.vue';
  import TodoList from '@/views/giantScreen/templates/default/components/todoList.vue';
  import StudentDisordersCount from '@/views/giantScreen/templates/default/components/studentDisordersCount.vue';
  import UserEduPie from '@/views/giantScreen/templates/default/components/userEduPie.vue';
  import UserTitleBar from '@/views/giantScreen/templates/default/components/userTitleBar.vue';
  import StudentAgeBar from '@/views/giantScreen/templates/default/components/studentAgeBar.vue';
  import ListModal from '@/views/giantScreen/templates/default/listModal.vue';
  import { onMounted, PropType, ref } from 'vue';
  import dayjs from 'dayjs';

  const props = defineProps({
    rawData: {
      type: Object as PropType<any>,
      required: true,
    },
  });

  const ready = ref(false);
  const currentTime = ref('');
  const listModalOptions = ref({
    visible: false,
    title: '',
  });
  const modals = ref({
    fusionSchool: {
      title: '融合学校',
      width: '60%',
    },
    resourceRoom: {
      title: '设立资源教室的学校',
      width: '60%',
    },
    specialTeacher: {
      title: '特校教师',
      width: '60%',
    },
    normalTeacher: {
      title: '资源教师',
      width: '60%',
    },
    allStudent: {
      title: '全部学生',
      width: '70%',
    },
    specialStudent: {
      title: '特教学校学生',
      width: '70%',
    },
    normalStudent: {
      title: '随班就读学生',
      width: '70%',
    },
    sendStudent: {
      title: '送教上门学生',
      width: '70%',
    },
    conference: {
      title: '工作记录',
      width: '50%',
    },
    sendRecord: {
      title: '近期送教记录',
      width: '80%',
    },
  });

  const handleListModalVisible = () => {
    listModalOptions.value = {
      visible: false,
      title: '',
    };
  };

  const showListModal = (type) => {
    listModalOptions.value = {
      visible: true,
      type,
      ...modals.value[type],
    };
  };

  onMounted(async () => {
    await Promise.all([await import.meta.glob('./listModal/*.vue'), await import.meta.glob('./components/*.vue')]);

    ready.value = true;

    currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    setInterval(() => {
      currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
    }, 1000);
  });
</script>

<style lang="scss">
  @use './assets/common' as *;
  .giant-screen-adapter {
    padding: 0;
    margin: 0;
    font-size: 16px;
    background: #141d37;
    color: #fff;
  }
</style>

<style lang="scss" scoped>
  $mainGradient: linear-gradient(rgb(25, 37, 62) 14%, rgb(34, 62, 97) 100%);
  $margin: 16px;
  .is-flex {
    display: flex;
  }
  .ml {
    margin-left: $margin;
  }
  .mr {
    margin-right: $margin;
  }
  .mt {
    margin-top: $margin;
  }
  .mb {
    margin-bottom: $margin;
  }
  .header {
    height: 80px;
    background-image: url('./assets/images/title-bg.png');
    background-size: contain;
    margin: 0 auto;
    background-repeat: no-repeat;
    width: 80%;
    position: relative;
    .time {
      position: absolute;
      left: -10%;
      top: 10px;
    }
    .title {
      text-align: center;
      font-size: 38px;
      font-weight: 500;
      line-height: 80px;
      letter-spacing: 2px;
    }
  }

  .mini-card {
    text-align: center;
    height: 138px;
    width: 250px;
    background: $mainGradient;
    .title {
      font-size: 32px;
      margin: 12px 0 16px;
    }
    .num {
      font-size: 38px;
      color: rgb(255, 185, 0);
    }
  }

  .card-view {
    box-shadow: rgba(255, 255, 255, 0) 0px 0px 1px;
    background: linear-gradient(rgb(25, 37, 62) 3.14%, rgb(34, 62, 97) 100%);
    display: flex;
    flex-direction: column;
    .title {
      background: linear-gradient(90deg, rgba(0, 95, 201, 0.5) 0%, rgba(34, 62, 97, 0) 100%);
      height: 40px;
      padding-left: $margin;
      line-height: 40px;
      font-size: 22px;
    }
    .content {
      flex: 1;
    }
  }

  .main-wrap {
    padding: 0 16px;
    height: calc(100% - 80px);
    margin-top: -20px;
    .left {
      height: 100%;
      display: flex;
      flex-direction: column;
      width: 516px;
      flex-wrap: nowrap;
      margin-left: $margin;
      .first-block {
        text-align: center;
        display: flex;
      }
    }
    .middle {
      display: flex;
      flex-direction: column;
      padding-top: 36px;
      flex: 1;
      margin: 0 $margin;
      .first-right {
        width: 350px;
        .mini-card {
          width: 100%;
          margin-top: 16px;
          display: flex;
          justify-content: space-around;
          line-height: 110px;
          .title {
            margin: 0;
          }
          .num {
            color: rgb(13, 224, 157);
          }
          &:first-child {
            margin: 0;
            height: 96px;
            line-height: 96px;
            font-size: 36px;
            .num {
              color: rgb(255, 185, 0);
            }
          }
        }
      }

      .second-block {
        display: flex;
        .left-item {
          height: 180px;
          width: 350px;
          justify-content: space-around;
          align-items: center;
          background: $mainGradient;
        }
        .card-view {
          height: 180px;
          flex: 1;
        }
      }
    }
    .right {
      margin-right: $margin;
      display: flex;
      flex-direction: column;
    }
  }

  .clickable {
    cursor: pointer;
    transition: all linear 0.2s;
    &:hover {
      box-shadow: 0 0 20px 10px rgba(255, 255, 255, 0.2);
    }
  }
</style>

<style>
  body,
  html {
    background: #141d37;
  }

  .print-visible-only {
    display: none !important;
  }
</style>
