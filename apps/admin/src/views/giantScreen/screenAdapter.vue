<template>
  <div class="giant-screen-adapter" :style="style">
    <slot />
  </div>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { debounce } from 'lodash';

  const props = defineProps({
    width: {
      type: Number,
      default: 1920,
    },
    height: {
      type: Number,
      default: 1080,
    },
  });

  const style = ref({
    width: `${props.width}px`,
    height: `${props.height}px`,
    transform: 'scale(1) translate(-50%, -50%)',
  });

  const getScale = () => {
    const w = window.innerWidth / props.width;
    const h = window.innerHeight / props.height;
    return w < h ? w : h;
  };

  const setScale = () => {
    style.value.transform = `scale(${getScale()}) translate(-50%, -50%)`;
  };

  onMounted(() => {
    setScale();
    window.onresize = debounce(setScale, 1000);
  });
</script>

<style lang="scss" scoped>
  .giant-screen-adapter {
    transform-origin: 0 0;
    position: absolute;
    left: 50%;
    top: 50%;
    transition: 0.3s;
    :deep > div {
      height: 100%;
    }
  }
</style>
