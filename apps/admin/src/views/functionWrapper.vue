<template>
  <div class="container">
    <Breadcrumb :items="breadcrumbItems" />
    <router-view :schema="schema"></router-view>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { SchemaHelper } from '@repo/infrastructure/schema';
  import { useRoute } from 'vue-router';
  import { useSchemaStore } from '@repo/infrastructure/store';
  import { Schema } from '@repo/infrastructure/types';
  import { Message } from '@arco-design/web-vue';

  const route = useRoute();
  const { getSchemaByApi } = useSchemaStore();
  const routeComposition = SchemaHelper.parseRoutePath(route.path);

  const api = `/${routeComposition.module}/${routeComposition.func}`;
  let schema: Schema;
  try {
    schema = SchemaHelper.getInstance(getSchemaByApi(api));
  } catch (e) {
    const msg = `Can't find designate schema named: ${api}`;
    Message.error(msg);
    throw new Error(msg);
  }

  const breadcrumbItems = ref<any>(SchemaHelper.getBreadcrumbItems(schema, routeComposition) || []);
</script>

<style scoped></style>
