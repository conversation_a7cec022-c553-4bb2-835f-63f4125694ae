import { AppRouteRecordRaw } from '../types';

const ManageRoutes: AppRouteRecordRaw = {
  path: '/giantScreen',
  component: () => import('@/views/giantScreen/layout.vue'),
  meta: {
    requiresAuth: true,
    order: 0,
    redirect: '/',
  },
  children: [
    {
      path: 'statistics',
      component: () => import('@/views/giantScreen/statistics/index.vue'),
    },
  ],
};
export default ManageRoutes;
