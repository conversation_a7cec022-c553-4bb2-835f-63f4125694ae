<script setup lang="ts">
  import { onMounted, PropType, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  const props = defineProps({
    urlPrefix: {
      type: String,
      required: true,
    },
    tabs: {
      type: Array as PropType<any[]>,
      required: true,
    },
  });

  const availableTabs = props.tabs.filter((tab) => tab.visible?.() ?? true);
  const router = useRouter();
  const route = useRoute();
  const activeTab = ref<any>('');
  const firstInitialized = ref<any>(false);

  const initFirst = () => {
    if (firstInitialized.value) {
      return;
    }
    firstInitialized.value = true;
    const index = props.urlPrefix.split('/').length;
    let currentTab = router.currentRoute.value.path.split('/')[index];
    if (!currentTab) {
      currentTab = availableTabs[0].key;
    }
    activeTab.value = currentTab;
    const path = `${props.urlPrefix}/${currentTab}`;
    if (router.currentRoute.value.path !== path) {
      router.push(path);
    }
  };

  const handleGoPage = (key: string) => {
    activeTab.value = key;
    router.push(`${props.urlPrefix}/${key}`);
  };

  watch(route, initFirst);
  onMounted(() => {
    initFirst();
  });
</script>

<template>
  <a-card v-if="availableTabs.length > 1" class="ml-2 my-2">
    <a-tabs v-model:active-key="activeTab" position="left">
      <a-tab-pane v-for="tab in availableTabs" :key="tab.key" :tab="tab.title">
        <template #title>
          <div @click="() => handleGoPage(tab.key)">
            {{ tab.title }}
          </div>
        </template>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<style scoped lang="less">
  :deep {
    .arco-card-body {
      padding-right: 0;
    }
    .arco-tabs-content {
      display: none;
    }
  }
</style>
