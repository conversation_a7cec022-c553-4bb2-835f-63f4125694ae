<script lang="ts" setup>
  import { computed, PropType, ref, watch } from 'vue';
  import { useRoute } from 'vue-router';

  const props = defineProps({
    width: {
      type: Number,
      default: 0,
    },
    itemWidth: {
      type: Number,
      default: 110,
    },
    tabs: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    active: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<any>,
      default: 'rounded',
    },
  });

  const activeTab = ref<any>(props.active || 0);
  const emit = defineEmits(['change']);
  const route = useRoute();

  const availableTabs = computed(() => {
    return props.tabs.filter((tab: any) => {
      if (typeof tab.visible === 'function') {
        return tab.visible();
      }
      if (typeof tab.visible === 'boolean') {
        return tab.visible;
      }
      return true;
    });
  });

  const handleTabChange = (key: string | number) => {
    if (route.path.indexOf(`/${key}/`) >= 0) {
      return;
    }
    activeTab.value = key;
    emit('change', key);
  };

  const componentWidth = computed(() => {
    const hasItemWidth = availableTabs.value.some((tab: any) => tab.width);
    if (hasItemWidth) {
      return availableTabs.value.reduce((acc: number, tab: any) => acc + (tab.width || 0), 0);
    }
    if (props.width) {
      return props.width;
    }
    return availableTabs.value.length * props.itemWidth;
  });

  watch(
    () => props.active,
    (val) => {
      activeTab.value = val;
    },
  );
</script>

<template>
  <a-space class="wrapper">
    <div><a-divider direction="vertical" /></div>
    <a-tabs
      v-if="availableTabs?.length > 0"
      :active-key="activeTab"
      :style="{ width: `${componentWidth}px` }"
      :type="type"
      class="tabs-center"
      @tab-click="handleTabChange"
    >
      <a-tab-pane v-for="tab in availableTabs" :key="tab.key" :title="tab.title">
        <template #title>
          <a-badge :count="tab.count || 0" :dot="tab.dot !== false">
            {{ tab.title }}
          </a-badge>
        </template>
      </a-tab-pane>
    </a-tabs>
  </a-space>
</template>

<style lang="less" scoped>
  .tabs-center {
    margin: 18px auto 0;
  }
  :deep(.arco-tabs-tab) {
    border-radius: 4px !important;
    transition: all linear 0.2s;
    margin: 0 6px;
    padding: 3px 8px;
    &:first-child {
      margin-left: 0;
    }
  }
  .wrapper {
    margin-left: 8px;
    overflow: hidden;
    height: 28px;
  }
</style>
