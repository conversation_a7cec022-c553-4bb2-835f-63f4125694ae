<template>
  <a-layout class="layout" :class="{ mobile: appStore.hideMenu }">
    <div v-if="navbar" class="layout-navbar">
      <slot name="navbar">
        <nav-bar :return-target="returnTarget">
          <template #navbar-center>
            <slot name="navbar-center">
              <Menu v-if="topMenu" />
            </slot>
          </template>
          <template #navbar-right>
            <slot name="navbar-right">
              <nav-bar-right>
                <template #prepend>
                  <slot name="navbar-prepend"></slot>
                </template>
                <template #append>
                  <slot name="navbar-append"></slot>
                </template>
              </nav-bar-right>
            </slot>
          </template>
          <template #page-title>
            <slot name="page-title"></slot>
          </template>
        </nav-bar>
      </slot>
    </div>
    <a-layout>
      <a-layout>
        <a-layout class="layout-content view-container" :style="paddingStyle">
          <TabBar v-if="appStore.tabBar" />
          <a-layout-content>
            <slot>
              <PageLayout />
            </slot>
          </a-layout-content>
        </a-layout>
      </a-layout>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
  import { computed, onMounted, provide, ref, watch } from 'vue';
  import { useAppStore, useUserStore } from '@/common/store';
  import NavBar from '@/common/components/navbar/index.vue';
  import Menu from '@/common/components/menu/index.vue';
  import TabBar from '@/common/components/tab-bar/index.vue';
  import useResponsive from '@/common/hooks/responsive';
  import NavBarRight from '@/common/layout/components/navBarRight.vue';
  import PageLayout from './page-layout.vue';

  defineProps({
    returnTarget: {
      type: String,
      default: '',
    },
  });

  const isInit = ref<any>(false);
  const appStore = useAppStore();
  const userStore = useUserStore();
  // const router = useRouter();
  // const route = useRoute();
  // const permission = usePermission();
  useResponsive(true);
  const navbarHeight = `50px`;
  const navbar = computed(() => appStore.navbar);
  const renderMenu = computed(() => appStore.menu && !appStore.topMenu);
  const hideMenu = computed(() => appStore.hideMenu);
  const menuWidth = computed(() => {
    return appStore.menuCollapse ? 48 : appStore.menuWidth;
  });
  const collapsed = computed(() => {
    return appStore.menuCollapse;
  });
  const paddingStyle = computed(() => {
    const paddingLeft = renderMenu.value && !hideMenu.value ? { paddingLeft: `${menuWidth.value}px` } : {};
    const paddingTop = navbar.value ? { paddingTop: navbarHeight } : {};
    return { ...paddingLeft, ...paddingTop };
  });
  const setCollapsed = (val: boolean) => {
    if (!isInit.value) return; // for page initialization menu state problem
    appStore.updateSettings({ menuCollapse: val });
  };
  watch(
    () => userStore.roles,
    () => {
      // if (roleValue && !permission.accessRouter(route))
      //   router.push({ name: 'notFound' });
    },
  );
  const topMenu = computed(() => appStore.topMenu && appStore.menu);
  const drawerVisible = ref<any>(false);
  const drawerCancel = () => {
    drawerVisible.value = false;
  };
  provide('toggleDrawerMenu', () => {
    drawerVisible.value = !drawerVisible.value;
  });
  onMounted(() => {
    isInit.value = true;
  });
</script>

<style scoped lang="less">
  @nav-size-height: 50px;
  @layout-max-width: 1100px;

  .layout {
    width: 100%;
    height: 100%;
  }

  .layout-navbar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    width: 100%;
    height: @nav-size-height;
  }

  .layout-sider {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    height: 100%;
    transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

    &::after {
      position: absolute;
      top: 0;
      right: -1px;
      display: block;
      width: 1px;
      height: 100%;
      background-color: var(--color-border);
      content: '';
    }

    > :deep(.arco-layout-sider-children) {
      overflow-y: hidden;
    }
  }

  .menu-wrapper {
    height: 100%;
    overflow: auto;
    overflow-x: hidden;

    :deep(.arco-menu) {
      ::-webkit-scrollbar {
        width: 12px;
        height: 4px;
      }

      ::-webkit-scrollbar-thumb {
        background-color: var(--color-text-4);
        background-clip: padding-box;
        border: 4px solid transparent;
        border-radius: 7px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background-color: var(--color-text-3);
      }
    }
  }

  .layout-content {
    min-height: 100vh;
    overflow-y: hidden;
    background-color: var(--color-fill-2);
    transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);
  }

  //.view-container {
  //  height: calc(100% - 500px);
  //  overflow-y: hidden;
  //}
</style>
