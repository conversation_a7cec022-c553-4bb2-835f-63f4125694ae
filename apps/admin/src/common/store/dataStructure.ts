import { defineStore } from 'pinia';
import { request } from '@repo/infrastructure/request';
import { PROJECT_URLS } from '@repo/env-config';

const useDataStructureStore = defineStore({
  id: 'dataStructure',
  state: () => ({
    schemasMap: {},
  }),
  actions: {
    async initSchemas() {
      if (Object.keys(this.schemasMap).length) {
        return;
      }
      const { data } = await request('/common/configure/dataStructure', {
        baseURL: PROJECT_URLS.MAIN_PROJECT_API,
      });

      this.schemasMap = data;
    },
    async getSchema(schemaName: string) {
      await this.initSchemas();
      return this.schemasMap[schemaName];
    },
  },
});

export default useDataStructureStore;
